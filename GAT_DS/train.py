import os
import torch
from sklearn.model_selection import train_test_split

from GATClassifier import GatNodeClassifier, calculate_node_dim, train_model

def get_device(device=None):
    if device is None:
        if torch.cuda.is_available():
            device = torch.device('cuda')
        elif hasattr(torch.backends, 'mps') and torch.backends.mps.is_available():
            device = torch.device('mps')
        else:
            device = torch.device('cpu')
    else:
        device = torch.device(device)
    return device

CONFIG = {
    'results_dir': '../Results',
    'epochs': 200,
    'learning_rate': 0.001,
    'patience': 15,
    'device': get_device(),
    'model_path': 'gat_model.pt',
}

def load_files_from_results(dir_path):
    data = []
    for filename in os.listdir(dir_path):
        if filename.endswith('.csv'):
            data.append(os.path.join(dir_path, filename))
    return data


if __name__ == '__main__':
    device = CONFIG['device']

    # <PERSON><PERSON><PERSON><PERSON> hodnoty tříd z datasetu
    MAX_KEY_CLASS = 26
    MAX_VALUE_CLASS = 8
    MAX_RESULT_CLASS = 18

    # Inicializace modelu
    node_in_dim = calculate_node_dim(MAX_KEY_CLASS, MAX_VALUE_CLASS)
    model = GatNodeClassifier(
        node_in_dim=node_in_dim,
        output_dim=MAX_RESULT_CLASS,  # 18 výstupních tříd
        num_value_classes=MAX_VALUE_CLASS + 1  # 9 tříd (0-8)
    ).to(device)

    # Trénink
    all_files = load_files_from_results(CONFIG['results_dir'])

    # Náhodné rozdělení dat na trénovací (80%) a validační (20%) sady
    train_files, val_files = train_test_split(
        all_files,
        test_size=0.2,
        random_state=42,  # Pro reprodukovatelnost výsledků
        shuffle=True
    )

    print(f"Celkem souborů: {len(all_files)}")
    print(f"Trénovací soubory: {len(train_files)}")
    print(f"Validační soubory: {len(val_files)}")

    train_losses, val_losses, val_f1 = train_model(
        model,
        train_files,
        val_files,
        device,
        MAX_KEY_CLASS,
        MAX_VALUE_CLASS,
        MAX_RESULT_CLASS,
        epochs=CONFIG['epochs'],
        lr=CONFIG['learning_rate'],
        patience=CONFIG['patience'],
        output_dir="training_results"
    )

    # Uložení modelu
    torch.save(model.state_dict(), CONFIG['model_path'])
