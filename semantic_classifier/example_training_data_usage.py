"""
Příklad použití modulu training_data_preparation.py

<PERSON><PERSON> skript ukazuje, jak používat funkce pro přípravu trénovacích dat
nezávisle na třídě Classifier.
"""

from training_data_preparation import (
    load_training_data_from_xlsx,
    prepare_training_data,
    create_category_id_mapping,
    preprocess_text
)


def main():
    """Příklad použití funkcí pro přípravu trénovacích dat."""
    
    # Cesta k tréninkovým datům
    data_path = "training_data/training_set.xlsx"  # Upravte podle vaší struktury
    
    print("=== PŘÍKLAD POUŽITÍ TRAINING_DATA_PREPARATION ===\n")
    
    # 1. Načtení dat z XLSX souboru
    print("1. Načítání trénovacích dat z XLSX souboru...")
    structured_data = load_training_data_from_xlsx(data_path)
    
    if not structured_data:
        print("Nepodařilo se načíst tréninková data. Zkontrolujte cestu k souboru.")
        return
    
    print(f"Načteno {len(structured_data)} kategorií.\n")
    
    # 2. Vytvoření mapování kategorií
    print("2. Vytváření mapování kategorií na číselné identifikátory...")
    category_mapping = create_category_id_mapping(structured_data)
    print("Mapování kategorií:")
    for category, id_num in category_mapping.items():
        print(f"  {category}: {id_num}")
    print()
    
    # 3. Příprava tripletů pro trénink
    print("3. Příprava tripletů pro TripletLoss trénink...")
    training_examples = prepare_training_data(structured_data, print_triplets=False)
    print(f"Připraveno {len(training_examples)} tripletů pro trénink.\n")
    
    # 4. Ukázka preprocessing funkce
    print("4. Ukázka preprocessing funkce:")
    sample_texts = [
        "Faktura č. 2024/001",
        "Datum:splatnosti",
        "Celková   částka,   včetně   DPH",
        "I.Č.O.: 12345678"
    ]
    
    for text in sample_texts:
        preprocessed = preprocess_text(text)
        print(f"  Původní:      '{text}'")
        print(f"  Preprocessed: '{preprocessed}'")
        print()
    
    # 5. Detailní výpis prvních několika tripletů
    print("5. Ukázka prvních 3 tripletů:")
    for i, example in enumerate(training_examples[:3]):
        print(f"  Triplet {i+1}:")
        print(f"    Anchor:   '{example.texts[0]}'")
        print(f"    Positive: '{example.texts[1]}'")
        print(f"    Negative: '{example.texts[2]}'")
        print()
    
    print("=== KONEC PŘÍKLADU ===")


if __name__ == "__main__":
    main()
