"""
Modul pro přípravu trénovacích dat pro semantic classifier.

Obsahuje funkce pro:
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> trénovacích dat z XLSX souborů
- Preprocessing textů
- Přípravu tripletů pro TripletLoss trénink
- Vytváření mapování kategorií
"""

import os
import pandas as pd
import numpy as np
import re
from sentence_transformers import InputExample


def preprocess_text(text):
    """
    Preprocessing funkce pro normalizaci textů před jejich zpracováním.
    Aplikuje se na všechny texty před kódováním do embeddingů.

    Args:
        text (str): Vstupní text k preprocessing.

    Returns:
        str: Preprocessovaný text.
    """
    if not isinstance(text, str):
        return str(text) if text is not None else ""

    text = re.sub(r'(?<=\w)\.(?=\w)', ' ', text)           # Replace dots between letters with space
    text = re.sub(r'[,:;]', ' ', text)                     # Replace punctuation with space
    text = re.sub(r'\s+', ' ', text).strip()               # Collapse multiple spaces
    return text


def load_training_data_from_xlsx(data_path):
    """
    Načte tréninková data z XLSX souboru s novou strukturou.
    Očekává jeden list pro každou kategorii.
    Struktura listu:
    A1: TEXT KOTVY PRO TRIPLET LOSS (popisná věta / prefix)
    A2: (ignorováno)
    B1: Hlavička (Pozitivní varianty)
    B2 a dál: TEXTY POZITIVNÍCH VARIANT
    C1: Hlavička (Negativní příklady)
    C2 a dál: TEXTY NEGATIVNÍCH PŘÍKLADŮ

    Args:
        data_path (str): Cesta k XLSX souboru nebo adresáři obsahujícímu soubor training_set.xlsx.

    Returns:
        list: Seznam slovníků ve formátu [{'anchor_triplet': '...', 'positives_variants': [...], 'negatives': [...]}, ...]
    """
    # Pokud je data_path adresář, přidáme název souboru
    if os.path.isdir(data_path):
        data_path = os.path.join(data_path, 'training_set.xlsx')

    training_data = []
    print(f"Načítám tréninková data ze souboru: {data_path} s novou strukturou.")

    if not os.path.exists(data_path):
        print(f"Chyba: Soubor '{data_path}' nenalezen.")
        return []

    try:
        # Načtení všech listů z XLSX souboru
        xlsx = pd.ExcelFile(data_path)

        sheet_names = xlsx.sheet_names

        class_info = {}
        for i, sheet_name in enumerate(sheet_names):
            class_info[i] = sheet_name

        if not sheet_names:
            print(f"Chyba: Soubor '{data_path}' neobsahuje žádné listy.")
            return []

        print(f"Nalezeno {len(sheet_names)} listů v souboru.")

        # Zpracování každého listu
        for sheet_name in sheet_names:
            try:
                # Načtení listu do DataFrame
                # Použijeme header=None, abychom neztratili první řádek jako hlavičku
                df = pd.read_excel(xlsx, sheet_name=sheet_name, header=None, dtype=str).replace({np.nan: None})

                # Kontrola, zda DataFrame obsahuje data a alespoň 3 sloupce
                if df.empty or len(df.columns) < 3:
                    print(
                        f"    Upozornění: List '{sheet_name}' neobsahuje dostatek sloupců nebo dat. Očekávány jsou 3 sloupce A, B, C. Přeskakuji.")
                    continue

                # Získání textu kotvy pro TripletLoss z buňky A2 (řádek 1, sloupec 0)
                # Ignorujeme první řádek, který je hlavička "Kotva"
                anchor_triplet_text = str(df.iloc[1, 0]).strip() if pd.notna(df.iloc[1, 0]) else None

                if not anchor_triplet_text:
                    print(f"    Upozornění: List '{sheet_name}' neobsahuje text kotvy v buňce A2. Přeskakuji.")
                    continue

                # Aplikujeme preprocessing na kotvu
                anchor_triplet_text = preprocess_text(anchor_triplet_text)

                # Získání pozitivních variant ze sloupce B (od řádku 1, tj. druhý řádek v Excelu)
                # Ignorujeme první řádek, který je hlavička "Pozitivní varianty"
                positives_variants = [preprocess_text(str(val).strip()) for val in df.iloc[1:, 1].tolist() if
                                      pd.notna(val) and str(val).strip() != '']

                # Získání negativních příkladů ze sloupce C (od řádku 1, tj. druhý řádek v Excelu)
                # Ignorujeme první řádek, který je hlavička "Negativní příklady"
                negatives = [preprocess_text(str(val).strip()) for val in df.iloc[1:, 2].tolist() if
                             pd.notna(val) and str(val).strip() != '']

                # Pro TripletLoss potřebujeme alespoň jednu pozitivní variantu a jeden negativní příklad
                # Kotvu pro TripletLoss už máme (anchor_triplet_text)
                if not positives_variants or not negatives:
                    print(
                        f"    Upozornění: List '{sheet_name}' nemá dostatek pozitivních variant nebo negativních příkladů (alespoň jeden od každého typu). Přeskakuji.")
                    continue

                training_data.append({
                    'category_name': sheet_name,  # Název kategorie je název listu
                    'anchor_triplet': anchor_triplet_text,  # Text kotvy pro TripletLoss z A1
                    'positives_variants': positives_variants,  # Seznam pozitivních variant z B2 a dál
                    'negatives': negatives  # Seznam negativních příkladů z C2 a dál
                })

                print(
                    f"    Načteno z listu '{sheet_name}': Kategorie='{sheet_name}', Kotva pro triplet='{anchor_triplet_text}', {len(positives_variants)} pozitivních variant, {len(negatives)} negativních.")

            except Exception as e:
                print(f"    Chyba při zpracování listu '{sheet_name}': {e}")

    except Exception as e:
        print(f"Chyba při načítání souboru '{data_path}': {e}")

    print(f"Načítání dat dokončeno. Celkem {len(training_data)} sad dat pro triplet trénink.")
    return class_info, training_data


def create_category_id_mapping(structured_data):
    """
    Vytvoří mapování názvů kategorií na číselné identifikátory.

    Args:
        structured_data (list): Seznam slovníků s tréninkovými daty.

    Returns:
        dict: Mapování názvů kategorií na číselné identifikátory.
    """
    category_mapping = {}
    for i, item in enumerate(structured_data, 1):
        category_name = item['category_name']
        if category_name not in category_mapping:
            category_mapping[category_name] = i

    print(f"Vytvořeno mapování pro {len(category_mapping)} kategorií.")
    return category_mapping


def prepare_training_data(structured_data, print_triplets=False):
    """
    Připraví tréninková data z formátu
    {'anchor_triplet': '...', 'positives_variants': [...], 'negatives': [...]}
    na seznam InputExample pro TripletLoss.

    Optimalizovaná verze pro efektivní využití negativních příkladů:
    - Kombinuje anchor s každým positive (vzájemně)
    - Kombinuje každý positive s každým jiným positive navzájem
    - K těmto kombinacím přiřazuje negativní příklady postupně (cyklicky)
    - Eliminuje zbytečné opakování stejných kotev a pozitivních s různými negativními

    Args:
        structured_data (list): Seznam slovníků ve formátu
                              {'anchor_triplet': '...', 'positives_variants': [...], 'negatives': [...]}.
        print_triplets (bool): Pokud True, vypíše všechny vytvořené triplety pro kontrolu tréninku.

    Returns:
        list: Seznam objektů sentence_transformers.InputExample.
    """
    examples = []
    triplet_count = 0

    if print_triplets:
        print("\n=== VÝPIS TRIPLETŮ POUŽITÝCH PRO TRÉNINK (OPTIMALIZOVANÁ VERZE) ===")

    for item in structured_data:
        # Vezmeme kotvu, pozitivní varianty a negativní příklady
        anchor_for_triplet = item['anchor_triplet']
        positives_for_triplet = item['positives_variants']
        negatives_for_triplet = item['negatives']
        category_name = item.get('category_name', 'Neznámá kategorie')

        # Již jsme kontrolovali v load_training_data_from_xlsx,
        # že máme alespoň jednoho pozitiva a jednoho negativa pro triplet.
        # Nicméně, pokud by logika načítání byla jiná, je dobré tuto kontrolu nechat.
        if not positives_for_triplet or not negatives_for_triplet:
            if print_triplets:
                print(f"    Upozornění: Kotva '{anchor_for_triplet}' nemá dostatek pozitivních nebo negativních příkladů pro TripletLoss. Přeskakuji triplet.")
            continue

        if print_triplets:
            print(f"\nKategorie: {category_name}")
            print(f"Kotva: '{anchor_for_triplet}'")
            print(f"Pozitivní varianty ({len(positives_for_triplet)}): {positives_for_triplet}")
            print(f"Negativní příklady ({len(negatives_for_triplet)}): {negatives_for_triplet}")

        # Vytvoříme všechny pozitivní varianty (anchor + positives)
        all_positives = [anchor_for_triplet] + positives_for_triplet

        # Vytvoříme všechny kombinace pozitivních navzájem (bez opakování se sebou samým)
        positive_pairs = []
        for i, positive1 in enumerate(all_positives):
            for j, positive2 in enumerate(all_positives):
                # Přeskočíme kombinace se sebou samým
                if i == j:
                    continue
                positive_pairs.append((positive1, positive2))

        if print_triplets:
            print(f"Všechny pozitivní (kotva + varianty) ({len(all_positives)}): {all_positives}")
            print(f"Kombinace pozitivních párů: {len(positive_pairs)}")
            print("Vytvořené triplety (optimalizované přiřazení negativních):")

        # Přiřadíme negativní příklady cyklicky k pozitivním párům
        negative_index = 0
        for pair_index, (positive1, positive2) in enumerate(positive_pairs):
            # Vybereme negativní příklad cyklicky
            negative_example = negatives_for_triplet[negative_index % len(negatives_for_triplet)]
            negative_index += 1

            triplet_count += 1
            examples.append(InputExample(texts=[positive1, positive2, negative_example]))

            if print_triplets:
                print(f"  {triplet_count:3d}. ['{positive1}', '{positive2}', '{negative_example}']")

    if print_triplets:
        print(f"\n=== KONEC VÝPISU TRIPLETŮ ===")
        print(f"Celkem vytvořeno {len(examples)} tripletů z {len(structured_data)} kategorií.")
        print(f"Negativní příklady jsou přiřazeny cyklicky k pozitivním párům.")
    else:
        print(f"Připraveno {len(examples)} tréninkových tripletů pro TripletLoss (optimalizovaná verze).")

    return examples
