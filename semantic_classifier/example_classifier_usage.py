"""
Příklad použití třídy Classifier s novými parametry konstruktoru.

Ukazuje různé způsoby inicializace klasifikátoru s výchozími
a vlastními hodnotami pro model_name a model_path.
"""

from Classifier import Classifier


def example_default_parameters():
    """Příklad použití s výchozími parametry."""
    print("=== PŘÍKLAD 1: Výchozí parametry ===")
    print("Model: sentence-transformers/paraphrase-multilingual-mpnet-base-v2")
    print("Cesta: mpnet")
    print()
    
    # Použití výchozích hodnot
    classifier = Classifier()
    
    print(f"Model name: {classifier.model_name}")
    print(f"Model dir: {classifier.model_dir}")
    print(f"Device: {classifier.device}")
    print()


def example_custom_model_name():
    """Příklad použití s vlastním názvem modelu."""
    print("=== PŘÍKLAD 2: <PERSON>last<PERSON><PERSON> název modelu ===")
    
    # Použit<PERSON> vlastního názvu modelu, výchozí cesta
    custom_model = "sentence-transformers/all-MiniLM-L6-v2"
    classifier = Classifier(model_name=custom_model)
    
    print(f"Model name: {classifier.model_name}")
    print(f"Model dir: {classifier.model_dir}")
    print()


def example_custom_path():
    """Příklad použití s vlastní cestou."""
    print("=== PŘÍKLAD 3: Vlastní cesta k modelu ===")
    
    # Použití výchozího modelu, vlastní cesta
    classifier = Classifier(model_path="my_custom_model")
    
    print(f"Model name: {classifier.model_name}")
    print(f"Model dir: {classifier.model_dir}")
    print()


def example_both_custom():
    """Příklad použití s vlastním modelem i cestou."""
    print("=== PŘÍKLAD 4: Vlastní model i cesta ===")
    
    # Použití vlastního modelu i cesty
    classifier = Classifier(
        model_name="sentence-transformers/all-MiniLM-L6-v2",
        model_path="custom_model_folder"
    )
    
    print(f"Model name: {classifier.model_name}")
    print(f"Model dir: {classifier.model_dir}")
    print()


def example_model_operations():
    """Příklad operací s modelem."""
    print("=== PŘÍKLAD 5: Operace s modelem ===")

    # Vytvoření klasifikátoru s výchozími parametry
    classifier = Classifier()

    # Ukázka preprocessing funkce
    sample_texts = [
        "Faktura č. 2024/001",
        "Datum splatnosti: 31.12.2024",
        "Celková částka včetně DPH"
    ]

    print("Ukázka preprocessing:")
    for text in sample_texts:
        preprocessed = classifier.preprocess(text)
        print(f"  Původní:      '{text}'")
        print(f"  Preprocessed: '{preprocessed}'")
        print()

    # Ukázka přímého uložení modelu
    print("Uložení modelu:")
    print(f"  Model lze uložit přímo: classifier.model.save('{classifier.model_dir}')")
    print(f"  Centroidy lze uložit: classifier.save_centroids('cesta/centroids.pkl')")
    print()

    # Poznámka: Pro skutečnou klasifikaci by bylo potřeba načíst centroidy
    print("Pro klasifikaci je potřeba nejprve načíst nebo vypočítat centroidy kategorií.")
    print("To se děje během fine-tuningu nebo načítáním uložených centroidů.")


def main():
    """Hlavní funkce s příklady použití."""
    print("PŘÍKLADY POUŽITÍ TŘÍDY CLASSIFIER")
    print("=" * 50)
    print()
    
    try:
        example_default_parameters()
        example_custom_model_name()
        example_custom_path()
        example_both_custom()
        example_model_operations()
        
    except Exception as e:
        print(f"Chyba při spuštění příkladů: {e}")
        print("Ujistěte se, že máte nainstalované potřebné závislosti:")
        print("- sentence-transformers")
        print("- torch")
        print("- sklearn")
        print("- pandas")
        print("- numpy")


if __name__ == "__main__":
    main()
