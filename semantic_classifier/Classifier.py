import unicodedata

import torch
from torch.utils.data import DataLoader, random_split
from sentence_transformers import SentenceTransformer, InputExample, losses
from sentence_transformers.evaluation import EmbeddingSimilarityEvaluator, TripletEvaluator
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import os
import pandas as pd
import pickle
import re
import gc
from tqdm.autonotebook import tqdm

from semantic_classifier.training_data_preparation import load_training_data_from_xlsx, create_category_id_mapping, \
    prepare_training_data
from utils.utils import get_key_class_id


class Classifier:
    """
    Klasifikátor frází z obchodních dokumentů pomocí sentence transformer modelu
    s klasifikací založenou na centrocích kategorií.

    Umožňuje fine-tuning modelu na datech typu kotva-pozitivní-negativní
    načtených z CSV souborů. Po tréninku/načtení modelu se vypočítají
    centroidy pro definované kategorie. Klasifikace pak probíhá porovnáním
    vstupní fráze s těmito centroidy. Model lze uložit ve formátu ONNX pro použití v C++.
    """

    def __init__(self, model_name="sentence-transformers/paraphrase-multilingual-mpnet-base-v2", model_path="mpnet"):
        """
        Inicializuje klasifikátor načtením sentence transformer modelu.

        Args:
            model_name (str): Název modelu pro stažení z externích zdrojů.
                             Výchozí: "sentence-transformers/paraphrase-multilingual-mpnet-base-v2"
            model_path (str): Cesta k lokální složce s modelem.
                             Výchozí: "mpnet"
        """

        # Nastavení device na 'cuda' pokud je k dispozici GPU, jinak 'cpu'
        # if torch.cuda.is_available():
        #     device = 'cuda'
        # elif torch.backends.mps.is_built() and torch.backends.mps.is_available():
        #     device = 'mps'
        #     print("Používám Apple Metal Performance Accelerators (MPS)")
        # else:
        #     device = 'cpu'
        device = 'cpu'
        self.device = device
        self.model_name = model_name
        self.model_dir = model_path
        self.category_centroids = None
        self.category_id_mapping = {}

        # Zkontrolujeme existenci složky modelu a načteme model
        if os.path.exists(self.model_dir) and os.path.exists(os.path.join(self.model_dir, 'config.json')):
            print(f"Načítám lokální model ze složky: {self.model_dir}")
            try:
                self.model = SentenceTransformer(self.model_dir, device=self.device)
                print(f"Lokální model úspěšně načten (zařízení: {self.device})")

                # Pokusíme se načíst centroidy, pokud existují
                centroids_path = os.path.join(self.model_dir, 'centroids.pkl')
                if os.path.exists(centroids_path):
                    self.load_centroids(centroids_path)
                    print("Centroidy automaticky načteny s lokálním modelem.")
            except Exception as e:
                print(f"Chyba při načítání lokálního modelu: {e}")
                print("Stahuji model z externích zdrojů...")
                self.model = SentenceTransformer(model_name, device=self.device)
                print(f"Model stažen z externích zdrojů (zařízení: {self.device})")
        else:
            print(f"Lokální model nenalezen, stahuji z externích zdrojů: {model_name}")
            self.model = SentenceTransformer(model_name, device=self.device)
            print(f"Model stažen z externích zdrojů (zařízení: {self.device})")

        print(f"Inicializace klasifikátoru dokončena (zařízení: {self.device})")

    def set_category_id_mapping(self, mapping):
        """
        Nastaví mapování názvů kategorií na číselné identifikátory.

        Poznámka: Mapování se automaticky načítá z Excel souboru během fine-tuningu.
        Tato metoda je určena pro explicitní nastavení mapování bez fine-tuningu.

        Args:
            mapping (dict): Slovník mapující názvy kategorií (str) na číselné identifikátory (int).
                           Například: {"Faktura": 1, "Datum": 2, "Částka": 3}
                           Doporučuje se použít utils.load_key_class_mapping_from_xlsx()
        """
        if not isinstance(mapping, dict):
            raise ValueError("Mapování musí být slovník (dict).")

        # Ověříme, že všechny hodnoty jsou číselné
        for category_id, category_name in mapping.items():
            if not isinstance(category_id, int):
                raise ValueError(f"Identifikátor kategorie '{category_name}' musí být celé číslo, ale je {type(category_id)}.")

        self.category_id_mapping = mapping.copy()
        print(f"Nastaveno mapování kategorií pro {len(mapping)} kategorií.")

    def get_category_id(self, category_name):
        """
        Vrátí číselný identifikátor pro daný název kategorie.

        Args:
            category_name (str): Název kategorie.

        Returns:
            int: Číselný identifikátor kategorie nebo 0 pro neznámou kategorii.
        """
        if category_name is None:
            return 0

        if not self.category_id_mapping:
            print("Upozornění: Mapování kategorií není nastaveno. Použijte fine_tune() nebo set_category_id_mapping().")
            return 0

        return self.category_id_mapping.get(category_name, 0)

    def preprocess(self, text):
        """
        Preprocessing funkce pro normalizaci textů před jejich zpracováním.
        Aplikuje se na všechny texty před kódováním do embeddingů.

        Args:
            text (str): Vstupní text k preprocessing.

        Returns:
            str: Preprocessovaný text.
        """
        if not isinstance(text, str):
            return str(text) if text is not None else ""

        text = re.sub(r'[.,:;—-‘"+/&<>~¢|®©]', ' ', text)
        text = re.sub(r'\*', ' ', text).strip()
        text = re.sub(r'\(', ' ', text).strip()
        text = re.sub(r'\)', ' ', text).strip()
        text = re.sub(r'\[', ' ', text).strip()
        text = re.sub(r'\]', ' ', text).strip()
        text = re.sub(r'\\', ' ', text).strip()
        text = re.sub(r'\!', ' ', text).strip()
        text = re.sub(r'\?', ' ', text).strip()
        text = re.sub(r'\s+', ' ', text).strip()               # Collapse multiple spaces
        return text



    def _manage_memory(self):
        """
        Pomocná metoda pro správu paměti pro všechna zařízení.
        Pokusí se uvolnit nepoužívanou paměť.
        """
        # Vynucení garbage collection
        gc.collect()

        # Vyčištění cache pro všechny dostupné backendy
        if self.device == 'mps':
            # Pro MPS zařízení (Apple Silicon)
            if hasattr(torch, 'mps') and hasattr(torch.mps, 'empty_cache'):
                torch.mps.empty_cache()
        elif self.device == 'cuda':
            # Pro CUDA zařízení
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

        # Další kolo garbage collection po vyčištění cache
        gc.collect()

    def encode(self, texts, batch_size=16):
        """
        Generuje embeddingy pro seznam textů.
        Automaticky aplikuje preprocessing na všechny texty před kódováním.

        Args:
            texts (list): Seznam textových řetězců k zakódování.
            batch_size (int): Velikost dávky pro zpracování.

        Returns:
            numpy.ndarray: Pole embeddingů.
        """
        # Používáme model.encode pro generování embeddingů.
        # convert_to_numpy=True zajistí, že výstup bude NumPy pole.
        # device je nastaveno v __init__
        if not isinstance(texts, list):
             texts = [texts] # Zajistíme, že vstup je seznam, i když je to jen jedna fráze

        # Aplikujeme preprocessing na všechny texty
        preprocessed_texts = [self.preprocess(text) for text in texts]

        # Adjust batch size for MPS to avoid memory issues
        actual_batch_size = batch_size
        if self.device == 'mps':
            # Use a smaller batch size on MPS to avoid memory issues
            actual_batch_size = min(batch_size, 32)  # Limit batch size on MPS
            if len(preprocessed_texts) > actual_batch_size and batch_size > actual_batch_size:
                print(f"Using reduced batch size {actual_batch_size} for encoding on MPS device (original: {batch_size})")

        return self.model.encode(preprocessed_texts, convert_to_numpy=True, batch_size=actual_batch_size, show_progress_bar=False)


    def calculate_category_centroids(self, structured_data, save_path=None):
        """
        Vypočítá a uloží centroidy pro každou sémantickou kategorii
        na základě průměru embeddingů kotevní fráze a jejích pozitivních příkladů.

        Args:
            structured_data (list): Seznam slovníků ve formátu
                                  {'category_name': '...', 'anchor_triplet': '...', 'positives_variants': [...], 'negatives': [...]}.
                                  Toto jsou data načtená funkcí load_training_data_from_xlsx z training_data_preparation.
            save_path (str, optional): Cesta pro uložení vypočtených centroidů. Pokud None, centroidy se neuloží na disk.
        """
        print("Vypočítávám centroidy kategorií...")
        if not structured_data:
            print("Nenalezena strukturovaná data pro výpočet centroidů.")
            self.category_centroids = None
            return

        # Shromáždíme všechny fráze a vytvoříme mapování indexů na kategorie
        all_phrases_for_encoding = []
        category_index_map = {}
        current_embedding_index = 0

        for item in structured_data:
            # Použijeme název listu jako název kategorie
            category_name = item['category_name']
            # Použijeme anchor_triplet a positives_variants jako fráze pro výpočet centroidu
            phrases = [item['anchor_triplet']] + item['positives_variants']

            if not phrases:
                print(f"    Upozornění: Kategorie '{category_name}' nemá žádné fráze (kotva+pozitivní) pro výpočet centroidu. Přeskakuji.")
                continue

            category_index_map[category_name] = (current_embedding_index, current_embedding_index + len(phrases))
            all_phrases_for_encoding.extend(phrases)
            current_embedding_index += len(phrases)

        if not all_phrases_for_encoding:
            print("Nenalezeny žádné fráze pro výpočet centroidů.")
            self.category_centroids = None
            return

        # Zakódujeme všechny fráze najednou
        print(f"  Zakódovávám {len(all_phrases_for_encoding)} frází pro výpočet centroidů...")
        # Use a more memory-efficient batch size, especially for MPS
        encode_batch_size = 32 if self.device == 'mps' else 64
        all_embeddings = self.encode(all_phrases_for_encoding, batch_size=encode_batch_size)
        print("  Kódování dokončeno.")

        # Uvolnění paměti po náročné operaci
        self._manage_memory()

        # Vypočítáme průměrné embeddingy (centroidy) pro každou kategorii podle mapování indexů
        self.category_centroids = {}
        for category_name, (start_idx, end_idx) in category_index_map.items():
            if start_idx >= end_idx:  # Prázdný rozsah
                continue

            category_embeddings = all_embeddings[start_idx:end_idx]
            # Vypočítáme průměr embeddingů podél osy 0 (průměr přes jednotlivé embedding vektory)
            centroid = np.mean(category_embeddings, axis=0)
            # Normalizujeme centroid, aby kosinusová podobnost fungovala správně
            centroid = centroid / np.linalg.norm(centroid)
            self.category_centroids[category_name] = centroid

        print(f"Výpočet centroidů dokončen pro {len(self.category_centroids)} kategorií.")

        # Uložení centroidů na disk, pokud je zadána cesta
        if save_path and self.category_centroids:
            self.save_centroids(save_path)

    def save_centroids(self, save_path):
        """
        Uloží vypočtené centroidy kategorií do souboru pro pozdější použití.

        Args:
            save_path (str): Cesta pro uložení centroidů. Může být cesta k souboru nebo adresáři.
                             Pokud je to adresář, soubor se vytvoří jako 'centroids.pkl' v tomto adresáři.
        """
        if self.category_centroids is None or not self.category_centroids:
            print("Chyba: Centroidy kategorií nejsou vypočítány. Nelze uložit.")
            return

        # Kontrola, zda je save_path adresář nebo soubor
        if os.path.isdir(save_path) or save_path.endswith('/'):
            os.makedirs(save_path, exist_ok=True)
            save_path = os.path.join(save_path, 'centroids.pkl')
        else:
            # Vytvoření adresáře, pokud neexistuje
            os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)

        try:
            # Uložíme jak centroidy, tak mapování kategorií
            data_to_save = {
                'centroids': self.category_centroids,
                'category_id_mapping': self.category_id_mapping
            }
            with open(save_path, 'wb') as f:
                pickle.dump(data_to_save, f)
            print(f"Centroidy a mapování kategorií úspěšně uloženy do: {save_path}")
        except Exception as e:
            print(f"Chyba při ukládání centroidů: {str(e)}")

    def load_centroids(self, load_path):
        """
        Načte centroidy kategorií ze souboru.

        Args:
            load_path (str): Cesta k souboru s uloženými centroidy.

        Returns:
            bool: True pokud se centroidy úspěšně načetly, jinak False.
        """
        try:
            # Kontrola, zda je load_path adresář nebo soubor
            if os.path.isdir(load_path):
                load_path = os.path.join(load_path, 'centroids.pkl')

            if not os.path.exists(load_path):
                print(f"Chyba: Soubor s centroidy '{load_path}' neexistuje.")
                return False

            with open(load_path, 'rb') as f:
                loaded_data = pickle.load(f)

            # Kontrola, zda je to nový formát s mapováním nebo starý formát pouze s centroidy
            if isinstance(loaded_data, dict) and 'centroids' in loaded_data:
                # Nový formát - obsahuje centroidy i mapování
                self.category_centroids = loaded_data['centroids']
                if 'category_id_mapping' in loaded_data:
                    self.category_id_mapping = loaded_data['category_id_mapping']
                    print(f"Načteno mapování pro {len(self.category_id_mapping)} kategorií.")
                else:
                    print("Mapování kategorií nebylo nalezeno v souboru, používám výchozí.")
            else:
                # Starý formát - pouze centroidy
                self.category_centroids = loaded_data
                print("Načten starý formát centroidů bez mapování kategorií, používám výchozí mapování.")

            print(f"Centroidy úspěšně načteny z: {load_path}")
            print(f"Načteno {len(self.category_centroids)} kategorií.")
            return True
        except Exception as e:
            print(f"Chyba při načítání centroidů: {str(e)}")
            self.category_centroids = None
            return False


    def contains_digit(self, text):
        """
        Kontroluje, zda text obsahuje číslici.

        Args:
            text (str): Text ke kontrole.

        Returns:
            bool: True pokud text obsahuje číslici, jinak False.
        """
        return bool(re.search(r'\d', text))

    def classify(self, phrase, use_numeric_ids=False):
        """
        Klasifikuje vstupní frázi do jedné z předdefinovaných sémantických kategorií
        na základě nejvyšší kosinusové podobnosti s centroidy kategorií.
        Přeskakuje texty, které obsahují číslice.
        Automaticky aplikuje preprocessing na vstupní frázi.

        Args:
            phrase (str): Fráze k zařazení.
            use_numeric_ids (bool): Pokud True, vrátí číselný identifikátor kategorie místo názvu.

        Returns:
            tuple: Dvojice (kategorie, nejvyšší_podobnost) kde kategorie je buď název (str)
                   nebo číselný identifikátor (int) podle parametru use_numeric_ids.
                   Vrátí (None/0, -1.0) pokud centroidy nebyly vypočítány, podobnost je nízká,
                   nebo text obsahuje číslice.
        """
        # Kontrola, zda text obsahuje číslice (před preprocessingem)
        if self.contains_digit(phrase):
            return None, -1.0

        # Zkontrolujeme, zda byly vypočítány centroidy
        if self.category_centroids is None or not self.category_centroids:
            print("Chyba: Centroidy kategorií nejsou vypočítány. Spusťte metodu calculate_category_centroids().")
            return None, -1.0

        # Zakódujeme vstupní frázi (preprocessing se aplikuje automaticky v encode metodě)
        phrase_embedding = self.encode([phrase])[0]

        best_category = None
        max_similarity = -1.0 # Kosinusová podobnost je v rozsahu [-1, 1]

        # Porovnáme embedding vstupní fráze s každým centroidem kategorie
        for category_name, centroid in self.category_centroids.items():
            # Reshape centroid pro správné dimenze pro cosine_similarity
            centroid_reshaped = centroid.reshape(1, -1)
            # Reshape phrase_embedding pro správné dimenze
            phrase_embedding_reshaped = phrase_embedding.reshape(1, -1)

            # Vypočítáme kosinusovou podobnost
            similarity = cosine_similarity(phrase_embedding_reshaped, centroid_reshaped)[0][0]

            # Pokud je tato podobnost lepší než dosavadní maximum, aktualizujeme
            if similarity > max_similarity:
                max_similarity = similarity
                best_category = category_name

        # Vrátíme nejlepší kategorii a její podobnost
        if use_numeric_ids:
            # Převedeme název kategorie na číselný identifikátor
            # category_id = self.get_category_id(best_category)
            category_id = get_key_class_id(best_category)
            return category_id, max_similarity
        else:
            # Vrátíme název kategorie
            return best_category, max_similarity

    def batch_classify(self, df, text_column='text', class_column='class', similarity_column=None, threshold=None, batch_size=None, use_numeric_ids=False):
        """
        Klasifikuje texty v DataFrame do předdefinovaných sémantických kategorií.
        Výsledky zapisuje přímo do vstupního DataFrame.
        Automaticky aplikuje preprocessing na všechny texty.

        VEKTORIZOVANÁ VERZE - výrazně rychlejší než původní implementace.

        Args:
            df (pandas.DataFrame): DataFrame obsahující texty ke klasifikaci
            text_column (str): Název sloupce obsahujícího texty ke klasifikaci (výchozí: 'text')
            class_column (str): Název sloupce, do kterého se zapíše výsledná kategorie (výchozí: 'class')
            similarity_column (str, optional): Název sloupce, do kterého se zapíše podobnost.
                                             Pokud None, podobnost se nezapisuje.
            threshold (float, optional): Práh podobnosti. Pokud je podobnost nižší než tento práh,
                                       kategorie se nastaví na None/0. Pokud None, práh se nepoužije.
            batch_size (int, optional): Velikost dávky pro kódování textů. Pokud None, použije se automatická hodnota.
            use_numeric_ids (bool): Pokud True, ukládá číselné identifikátory kategorií místo názvů.

        Returns:
            pandas.DataFrame: Vstupní DataFrame s přidanými sloupci pro výsledky klasifikace
        """
        # Zkontrolujeme, zda byly vypočtány centroidy
        if self.category_centroids is None or not self.category_centroids:
            print("Chyba: Centroidy kategorií nejsou vypočtány. Spusťte metodu calculate_category_centroids().")
            # Přidáme prázdné sloupce
            df[class_column] = None
            if similarity_column:
                df[similarity_column] = -1.0
            return df

        # Zkontrolujeme, zda existuje sloupec s texty
        if text_column not in df.columns:
            print(f"Chyba: Sloupec '{text_column}' nebyl nalezen v DataFrame.")
            return df

        # Vytvoříme kopii DataFrame, abychom nemodifikovali vstupní data
        result_df = df.copy()

        # Inicializujeme sloupce pro výsledky
        if use_numeric_ids:
            result_df[class_column] = 0  # Výchozí hodnota 0 pro číselné identifikátory
        else:
            result_df[class_column] = None  # Výchozí hodnota None pro názvy kategorií
        if similarity_column:
            result_df[similarity_column] = -1.0

        print(f"Spouštím vektorizovanou klasifikaci pro {len(result_df)} textů...")

        # VEKTORIZOVANÁ VERZE - filtrujeme platné texty
        # Vytvoříme masku pro platné texty (ne prázdné, ne None, neobsahují číslice)
        valid_mask = (
            result_df[text_column].notna() &
            (result_df[text_column] != '') &
            (result_df[text_column] != 'page') &
            (~result_df[text_column].astype(str).apply(self.contains_digit))
        )

        valid_indices = result_df[valid_mask].index.tolist()
        valid_texts = result_df.loc[valid_mask, text_column].tolist()

        if not valid_texts:
            print("Nenalezeny žádné platné texty ke klasifikaci.")
            return result_df

        print(f"Nalezeno {len(valid_texts)} platných textů ke klasifikaci (z {len(result_df)} celkem).")

        # Nastavíme batch_size pokud není zadán
        if batch_size is None:
            # Použijeme optimální batch_size podle zařízení
            if self.device == 'mps':
                batch_size = 32  # Menší batch pro MPS kvůli paměti
            elif self.device == 'cuda':
                batch_size = 64  # Větší batch pro CUDA
            else:
                batch_size = 32  # Střední hodnota pro CPU

        # Zakódujeme všechny platné texty najednou
        print(f"Kóduji {len(valid_texts)} textů (batch_size={batch_size})...")
        try:
            # Použijeme tqdm pro progress bar při kódování
            with tqdm(total=len(valid_texts), desc="Kódování textů", unit="text") as pbar:
                # Pro velmi velké datasety můžeme kódovat po částech a zobrazovat progress
                if len(valid_texts) > 1000:
                    # Pro velké datasety kódujeme po částech
                    embeddings_list = []
                    chunk_size = batch_size * 10  # Zpracujeme po 10 batch-ích najednou

                    for i in range(0, len(valid_texts), chunk_size):
                        chunk_texts = valid_texts[i:i + chunk_size]
                        chunk_embeddings = self.encode(chunk_texts, batch_size=batch_size)
                        embeddings_list.append(chunk_embeddings)
                        pbar.update(len(chunk_texts))

                    embeddings = np.vstack(embeddings_list)
                else:
                    # Pro menší datasety kódujeme najednou
                    embeddings = self.encode(valid_texts, batch_size=batch_size)
                    pbar.update(len(valid_texts))

            print("Kódování dokončeno.")
        except Exception as e:
            print(f"Chyba při kódování textů: {e}")
            print("Používám fallback na jednotlivou klasifikaci...")
            return self._fallback_classify(result_df, text_column, class_column, similarity_column, threshold, use_numeric_ids)

        # Připravíme centroidy pro vektorizovaný výpočet
        category_names = list(self.category_centroids.keys())
        centroid_matrix = np.array([self.category_centroids[name] for name in category_names])

        print(f"Počítám podobnosti pro {len(embeddings)} textů s {len(category_names)} kategoriemi...")

        # Vypočítáme podobnosti pro všechny texty najednou - toto je hlavní výkonnostní výhra
        try:
            with tqdm(total=1, desc="Výpočet podobností", unit="operace") as pbar:
                similarities = cosine_similarity(embeddings, centroid_matrix)
                pbar.update(1)
            print("Výpočet podobností dokončen.")
        except Exception as e:
            print(f"Chyba při výpočtu podobností: {e}")
            print("Používám fallback na jednotlivou klasifikaci...")
            return self._fallback_classify(result_df, text_column, class_column, similarity_column, threshold, use_numeric_ids)

        # Najdeme nejlepší kategorie a jejich podobnosti
        best_indices = np.argmax(similarities, axis=1)
        max_similarities = np.max(similarities, axis=1)

        # Zapíšeme výsledky do DataFrame
        print("Zapisuji výsledky...")
        with tqdm(total=len(valid_indices), desc="Zápis výsledků", unit="řádek") as pbar:
            for i, idx in enumerate(valid_indices):
                category_name = category_names[best_indices[i]]
                similarity = max_similarities[i]

                # Aplikujeme práh, pokud je zadán
                if threshold is not None and similarity < threshold:
                    if use_numeric_ids:
                        category = -1  # Výchozí hodnota pro číselné identifikátory
                    else:
                        category = None  # Výchozí hodnota pro názvy kategorií
                else:
                    if use_numeric_ids:
                        category = get_key_class_id(category_name) #self.get_category_id(category_name)
                    else:
                        category = category_name

                result_df.at[idx, class_column] = category
                if similarity_column:
                    result_df.at[idx, similarity_column] = similarity

                # Aktualizujeme progress bar každých 100 řádků pro lepší výkon
                if i % 100 == 0 or i == len(valid_indices) - 1:
                    pbar.update(min(100, len(valid_indices) - i))

        # Uvolnění paměti po náročné operaci
        self._manage_memory()

        print(f"Vektorizovaná klasifikace dokončena. Klasifikováno {len(valid_texts)} textů.")
        return result_df

    def _fallback_classify(self, df, text_column, class_column, similarity_column, threshold, use_numeric_ids=False):
        """
        Fallback metoda pro klasifikaci po jednom textu, pokud vektorizovaná verze selže.
        Používá se jako záložní řešení při chybách v batch zpracování.

        Args:
            df (pandas.DataFrame): DataFrame s inicializovanými sloupci pro výsledky
            text_column (str): Název sloupce s texty
            class_column (str): Název sloupce pro kategorie
            similarity_column (str): Název sloupce pro podobnosti (může být None)
            threshold (float): Práh podobnosti (může být None)
            use_numeric_ids (bool): Pokud True, ukládá číselné identifikátory kategorií místo názvů

        Returns:
            pandas.DataFrame: DataFrame s výsledky klasifikace
        """
        print("Používám fallback klasifikaci (text po textu)...")

        # Klasifikujeme každý řádek jednotlivě
        for idx, row in df.iterrows():
            # Získáme text ke klasifikaci
            text = row[text_column]

            # Pokud je text prázdný, None nebo obsahuje číslice, přeskočíme ho
            if pd.isna(text) or text == '' or self.contains_digit(text):
                continue

            try:
                # Klasifikujeme text s použitím správného parametru
                category, similarity = self.classify(text, use_numeric_ids=use_numeric_ids)

                # Aplikujeme práh, pokud je zadán
                if threshold is not None and similarity < threshold:
                    if use_numeric_ids:
                        category = 0  # Výchozí hodnota pro číselné identifikátory
                    else:
                        category = None  # Výchozí hodnota pro názvy kategorií

                # Zapíšeme výsledky
                df.at[idx, class_column] = category
                if similarity_column:
                    df.at[idx, similarity_column] = similarity

            except Exception as e:
                print(f"Chyba při klasifikaci textu na řádku {idx}: {e}")
                # Ponecháme výchozí hodnoty (None/0, -1.0)
                continue

        print("Fallback klasifikace dokončena.")
        return df








