#!/usr/bin/env python3
"""
Fine-tuning script pro semantic classifier.
Nyní používá standalone fine_tune_model funkci místo Classifier.fine_tune() metody.
"""

import os
from fine_tune_standalone import fine_tune_model

# Nastavení prostředí
os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Parametry fine-tuningu
model_name = 'sentence-transformers/paraphrase-multilingual-mpnet-base-v2'
data_dir = 'training_data'
model_dir = 'mpnet'
batch_size = 32

print("🚀 Spouštím fine-tuning pomocí standalone funkce...")

# Spustíme fine-tuning pomocí standalone funkce
structured_data = fine_tune_model(
    model_name=model_name,
    data_dir=data_dir,
    model_dir=model_dir,
    print_triplets=False,
    num_epochs=5,
    train_batch_size=batch_size,
    learning_rate=2e-5,
    validation_split=0.3
)

print(f"\n{'='*60}")
print("Fine-tuning dokončen!")
print(f"Model uložen v: {model_dir}")
print(f"Načteno {len(structured_data)} kategorií z trénovacích dat.")