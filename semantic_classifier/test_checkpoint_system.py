#!/usr/bin/env python3
"""
Test script pro ověření checkpoint systému v extended fine-tuning.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Přidáme cestu pro importy
sys.path.append('.')

def test_checkpoint_creation():
    """Test vytváření checkpointů."""
    print("🧪 Testování vytváření checkpointů...")
    
    # Simulujeme vytvoření checkpointů
    with tempfile.TemporaryDirectory() as temp_dir:
        model_dir = os.path.join(temp_dir, "test_model")
        os.makedirs(model_dir, exist_ok=True)
        
        # Simulujeme vytvoření iteration checkpointů
        iterations = [1, 2, 3]
        for iteration in iterations:
            iteration_dir = os.path.join(model_dir, f"iteration_{iteration}")
            os.makedirs(iteration_dir, exist_ok=True)
            
            # Vytvoříme dummy soubory
            config_file = os.path.join(iteration_dir, "config.json")
            model_file = os.path.join(iteration_dir, "pytorch_model.bin")
            
            with open(config_file, 'w') as f:
                f.write('{"model_type": "test"}')
            with open(model_file, 'w') as f:
                f.write('dummy model data')
        
        # Zkontrolujeme, že checkpointy existují
        existing_checkpoints = []
        for iteration in iterations:
            iteration_dir = os.path.join(model_dir, f"iteration_{iteration}")
            if os.path.exists(iteration_dir):
                existing_checkpoints.append(iteration)
        
        print(f"   Vytvořené checkpointy: {existing_checkpoints}")
        
        if len(existing_checkpoints) == len(iterations):
            print("✅ Vytváření checkpointů funguje")
            return True, model_dir, existing_checkpoints
        else:
            print("❌ Některé checkpointy nebyly vytvořeny")
            return False, None, []

def test_checkpoint_cleanup():
    """Test čištění checkpointů."""
    print("\n🧪 Testování čištění checkpointů...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        model_dir = os.path.join(temp_dir, "test_model")
        os.makedirs(model_dir, exist_ok=True)
        
        # Vytvoříme testovací checkpointy
        iterations = [1, 2, 3]
        for iteration in iterations:
            iteration_dir = os.path.join(model_dir, f"iteration_{iteration}")
            os.makedirs(iteration_dir, exist_ok=True)
            
            # Vytvoříme dummy soubory
            with open(os.path.join(iteration_dir, "config.json"), 'w') as f:
                f.write('{"model_type": "test"}')
        
        print(f"   Checkpointy před čištěním: {iterations}")
        
        # Simulujeme čištění checkpointů
        cleaned_checkpoints = []
        for iteration in iterations:
            iteration_path = os.path.join(model_dir, f"iteration_{iteration}")
            if os.path.exists(iteration_path):
                try:
                    shutil.rmtree(iteration_path)
                    cleaned_checkpoints.append(iteration)
                    print(f"   Smazán checkpoint: iteration_{iteration}")
                except Exception as e:
                    print(f"   ⚠️  Nepodařilo se smazat iteration_{iteration}: {e}")
        
        # Zkontrolujeme, že checkpointy byly smazány
        remaining_checkpoints = []
        for iteration in iterations:
            iteration_dir = os.path.join(model_dir, f"iteration_{iteration}")
            if os.path.exists(iteration_dir):
                remaining_checkpoints.append(iteration)
        
        print(f"   Zbývající checkpointy: {remaining_checkpoints}")
        
        if len(remaining_checkpoints) == 0:
            print("✅ Čištění checkpointů funguje")
            return True
        else:
            print("❌ Některé checkpointy nebyly smazány")
            return False

def test_checkpoint_size_calculation():
    """Test výpočtu velikosti checkpointů."""
    print("\n🧪 Testování výpočtu velikosti checkpointů...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        model_dir = os.path.join(temp_dir, "test_model")
        os.makedirs(model_dir, exist_ok=True)
        
        # Vytvoříme checkpointy různých velikostí
        checkpoint_sizes = {}
        iterations = [1, 2, 3]
        
        for iteration in iterations:
            iteration_dir = os.path.join(model_dir, f"iteration_{iteration}")
            os.makedirs(iteration_dir, exist_ok=True)
            
            # Vytvoříme soubory různých velikostí
            config_file = os.path.join(iteration_dir, "config.json")
            model_file = os.path.join(iteration_dir, "pytorch_model.bin")
            
            with open(config_file, 'w') as f:
                f.write('{"model_type": "test"}' * 10)  # Malý soubor
            
            with open(model_file, 'w') as f:
                f.write('dummy model data' * 1000 * iteration)  # Větší soubory pro vyšší iterace
        
        # Vypočítáme velikosti
        total_size = 0
        for iteration in iterations:
            iteration_dir = os.path.join(model_dir, f"iteration_{iteration}")
            if os.path.exists(iteration_dir):
                size = sum(os.path.getsize(os.path.join(iteration_dir, f)) 
                          for f in os.listdir(iteration_dir) 
                          if os.path.isfile(os.path.join(iteration_dir, f)))
                checkpoint_sizes[iteration] = size
                total_size += size
        
        print(f"   Velikosti checkpointů:")
        for iteration, size in checkpoint_sizes.items():
            print(f"     iteration_{iteration}: {size / 1024:.1f} KB")
        print(f"   Celková velikost: {total_size / 1024:.1f} KB")
        
        if total_size > 0:
            print("✅ Výpočet velikosti checkpointů funguje")
            return True
        else:
            print("❌ Chyba při výpočtu velikosti")
            return False

def test_checkpoint_directory_structure():
    """Test struktury adresářů checkpointů."""
    print("\n🧪 Testování struktury adresářů...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        model_dir = os.path.join(temp_dir, "test_model")
        
        # Testujeme různé scénáře
        scenarios = [
            {"name": "Základní struktura", "iterations": [1, 2, 3]},
            {"name": "Jedna iterace", "iterations": [1]},
            {"name": "Mnoho iterací", "iterations": list(range(1, 11))},
            {"name": "Nesouvislé iterace", "iterations": [1, 3, 5, 7]}
        ]
        
        for scenario in scenarios:
            print(f"   Scénář: {scenario['name']}")
            
            # Vyčistíme adresář
            if os.path.exists(model_dir):
                shutil.rmtree(model_dir)
            os.makedirs(model_dir, exist_ok=True)
            
            # Vytvoříme checkpointy
            for iteration in scenario['iterations']:
                iteration_dir = os.path.join(model_dir, f"iteration_{iteration}")
                os.makedirs(iteration_dir, exist_ok=True)
                
                with open(os.path.join(iteration_dir, "config.json"), 'w') as f:
                    f.write(f'{{"iteration": {iteration}}}')
            
            # Zkontrolujeme strukturu
            created_dirs = []
            for item in os.listdir(model_dir):
                if item.startswith("iteration_") and os.path.isdir(os.path.join(model_dir, item)):
                    created_dirs.append(item)
            
            expected_dirs = [f"iteration_{i}" for i in scenario['iterations']]
            created_dirs.sort()
            expected_dirs.sort()
            
            if created_dirs == expected_dirs:
                print(f"     ✅ Struktura OK: {len(created_dirs)} checkpointů")
            else:
                print(f"     ❌ Struktura chybná: očekáváno {expected_dirs}, vytvořeno {created_dirs}")
                return False
        
        print("✅ Struktura adresářů funguje správně")
        return True

if __name__ == "__main__":
    print("💾 Test checkpoint systému pro Extended Fine-tuning")
    print("=" * 60)
    
    tests = [
        ("Checkpoint creation", test_checkpoint_creation),
        ("Checkpoint cleanup", test_checkpoint_cleanup),
        ("Checkpoint size calculation", test_checkpoint_size_calculation),
        ("Directory structure", test_checkpoint_directory_structure)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🔍 {test_name}:")
        print("-" * 40)
        try:
            if test_name == "Checkpoint creation":
                result, _, _ = test_func()
                if result:
                    passed += 1
                    print(f"✅ {test_name} - PROŠEL")
                else:
                    print(f"❌ {test_name} - SELHAL")
            else:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} - PROŠEL")
                else:
                    print(f"❌ {test_name} - SELHAL")
        except Exception as e:
            print(f"❌ {test_name} - CHYBA: {e}")
        print()
    
    print("=" * 60)
    print(f"📊 VÝSLEDKY: {passed}/{total} testů prošlo")
    
    if passed == total:
        print("🎉 Všechny testy checkpoint systému prošly!")
        print("\n💡 Checkpoint systém je připraven:")
        print("   💾 Automatické ukládání po každé iteraci")
        print("   🧹 Volitelné čištění checkpointů")
        print("   🔄 Reload modelu pro uvolnění paměti")
        print("   📊 Monitoring velikosti a struktury")
        print("   🛡️  Ochrana proti ztrátě pokroku")
    else:
        print("⚠️  Některé testy selhaly, ale základní funkcionalita může stále fungovat.")
