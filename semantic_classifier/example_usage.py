#!/usr/bin/env python3
"""
Příklad použití zjednodušené třídy Classifier.
"""

import os
import sys
import pandas as pd

# Přidáme cestu k utils modulu
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from Classifier import Classifier
from utils import utils


def example_basic_usage():
    """Základní příklad použití klasifikátoru."""
    print("=== Základní použití klasifikátoru ===")
    
    # 1. Inicializace klasifikátoru
    print("1. Inicializace klasifikátoru...")
    classifier = Classifier('paraphrase-multilingual-mpnet-base-v2')
    
    # 2. Nastavení mapování kategorií (pokud nemáme training_set.xlsx)
    print("2. Nastavení mapování kategorií...")
    category_mapping = {
        "Číslo faktury": 1,
        "Datum vystavení": 2,
        "Datum splatnosti": 3,
        "Celková částka": 4,
        "Dodavatel": 5
    }
    classifier.set_category_id_mapping(category_mapping)
    
    # 3. Uložení modelu
    print("3. Uložení modelu...")
    model_dir = "example_model"
    success = classifier.save_model(model_dir, save_centroids=False)
    if success:
        print(f"✓ Model uložen do: {model_dir}")
    
    # 4. Načtení modelu
    print("4. Načtení modelu...")
    success = classifier.load_model(model_dir, load_centroids=False)
    if success:
        print("✓ Model načten")
    
    # 5. Test kódování textů
    print("5. Test kódování textů...")
    test_texts = ["Číslo faktury", "Datum vystavení", "Celková částka"]
    embeddings = classifier.encode(test_texts)
    print(f"✓ Zakódováno {len(test_texts)} textů, rozměr: {embeddings.shape}")
    
    # Vyčištění
    try:
        import shutil
        if os.path.exists(model_dir):
            shutil.rmtree(model_dir)
            print("✓ Testovací složka vyčištěna")
    except:
        pass


def example_with_dataframe():
    """Příklad práce s DataFrame."""
    print("\n=== Práce s DataFrame ===")
    
    # Inicializace
    classifier = Classifier('paraphrase-multilingual-mpnet-base-v2')
    
    # Vytvoření testovacího DataFrame
    test_data = {
        'text': [
            'Číslo faktury',
            'Datum vystavení',
            'Celková částka k úhradě',
            '12345',  # Text s číslicemi
            'Dodavatel',
            'IČO',
            'DIČ',
            'Variabilní symbol'
        ],
        'left': [100, 200, 300, 400, 500, 600, 700, 800],
        'top': [50, 100, 150, 200, 250, 300, 350, 400],
        'width': [80, 90, 120, 60, 100, 50, 50, 110],
        'height': [20, 20, 20, 20, 20, 20, 20, 20]
    }
    
    df = pd.DataFrame(test_data)
    print(f"Vytvořen testovací DataFrame s {len(df)} řádky")
    print(df[['text']].head())
    
    # Test batch klasifikace (bez centroidů)
    print("\nTest batch klasifikace (bez centroidů)...")
    result_df = classifier.batch_classify(
        df=df,
        text_column='text',
        class_column='predicted_class',
        similarity_column='similarity',
        use_numeric_ids=True
    )
    
    print("Výsledky klasifikace:")
    print(result_df[['text', 'predicted_class', 'similarity']].head())


def example_save_onnx():
    """Příklad přípravy pro ONNX export."""
    print("\n=== Příprava pro ONNX export ===")

    try:
        # Inicializace
        classifier = Classifier('paraphrase-multilingual-mpnet-base-v2')

        # Příprava pro ONNX export
        onnx_path = "example_model.onnx"
        print(f"Připravuji model pro ONNX export: {onnx_path}")

        success = classifier.save_onnx(onnx_path)
        if success:
            print("✓ Model připraven pro ONNX export")

            # Kontrola vytvořených souborů
            model_dir = onnx_path.replace('.onnx', '_pytorch_model')
            info_file = onnx_path.replace('.onnx', '_onnx_export_info.txt')

            if os.path.exists(model_dir):
                print(f"✓ PyTorch model uložen do: {model_dir}")

            if os.path.exists(info_file):
                print(f"✓ Instrukce pro ONNX export: {info_file}")

        else:
            print("❌ Příprava pro ONNX export selhala")

        # Vyčištění
        import shutil
        model_dir = onnx_path.replace('.onnx', '_pytorch_model')
        info_file = onnx_path.replace('.onnx', '_onnx_export_info.txt')

        if os.path.exists(model_dir):
            shutil.rmtree(model_dir)
        if os.path.exists(info_file):
            os.remove(info_file)
        print("✓ Testovací soubory vyčištěny")

    except Exception as e:
        print(f"❌ Chyba při přípravě ONNX exportu: {e}")


def example_memory_management():
    """Příklad správy paměti."""
    print("\n=== Správa paměti ===")
    
    classifier = Classifier('paraphrase-multilingual-mpnet-base-v2')
    
    # Test kódování většího množství textů
    large_text_list = [f"Test text číslo {i}" for i in range(100)]
    
    print(f"Kóduji {len(large_text_list)} textů...")
    embeddings = classifier.encode(large_text_list, batch_size=16)
    print(f"✓ Zakódováno, rozměr: {embeddings.shape}")
    
    # Manuální správa paměti
    print("Spouštím správu paměti...")
    classifier._manage_memory()
    print("✓ Správa paměti dokončena")


def main():
    """Hlavní funkce s příklady použití."""
    print("📚 Příklady použití zjednodušené třídy Classifier")
    print("=" * 60)
    
    try:
        example_basic_usage()
        example_with_dataframe()
        example_save_onnx()
        example_memory_management()
        
        print("\n" + "=" * 60)
        print("🎉 Všechny příklady dokončeny!")
        
        print("\n📝 Další kroky pro plné využití:")
        print("1. Vytvořte training_set.xlsx s tréninkovými daty")
        print("2. Použijte fine_tune() pro natrénování modelu")
        print("3. Poté můžete používat classify() a batch_classify() s centroidy")
        print("4. Model lze exportovat do ONNX pro použití v C++")
        
    except Exception as e:
        print(f"❌ Chyba při spouštění příkladů: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
