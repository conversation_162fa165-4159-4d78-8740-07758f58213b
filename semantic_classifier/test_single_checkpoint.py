#!/usr/bin/env python3
"""
Test script pro ověření single checkpoint systému v extended fine-tuning.
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path

# Přidáme cestu pro importy
sys.path.append('.')

def test_single_checkpoint_creation():
    """Test vytváření single checkpoint."""
    print("🧪 Testování single checkpoint systému...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        model_dir = os.path.join(temp_dir, "test_model")
        os.makedirs(model_dir, exist_ok=True)
        
        checkpoint_path = os.path.join(model_dir, "latest_checkpoint")
        
        # Simulujeme několik iterací
        iterations = [1, 2, 3, 4, 5]
        
        for iteration in iterations:
            print(f"   Iterace {iteration}:")
            
            # Simulujeme uložení checkpointu (přep<PERSON><PERSON><PERSON> předchozí)
            os.makedirs(checkpoint_path, exist_ok=True)
            
            # Vyt<PERSON><PERSON><PERSON>me dummy soubory s informací o iteraci
            config_file = os.path.join(checkpoint_path, "config.json")
            model_file = os.path.join(checkpoint_path, "pytorch_model.bin")
            
            with open(config_file, 'w') as f:
                f.write(f'{{"iteration": {iteration}, "model_type": "test"}}')
            with open(model_file, 'w') as f:
                f.write(f'model data from iteration {iteration}')
            
            # Zkontrolujeme, že checkpoint existuje
            if os.path.exists(checkpoint_path):
                # Přečteme obsah pro ověření
                with open(config_file, 'r') as f:
                    content = f.read()
                print(f"     ✅ Checkpoint vytvořen/přepsán: {content}")
            else:
                print(f"     ❌ Checkpoint nebyl vytvořen")
                return False
        
        # Zkontrolujeme, že existuje pouze jeden checkpoint
        checkpoint_dirs = [d for d in os.listdir(model_dir) if d.startswith("latest_checkpoint") or d.startswith("iteration_")]
        
        print(f"   Nalezené checkpoint adresáře: {checkpoint_dirs}")
        
        if len(checkpoint_dirs) == 1 and checkpoint_dirs[0] == "latest_checkpoint":
            print("✅ Single checkpoint systém funguje správně!")
            return True
        else:
            print("❌ Problém se single checkpoint systémem!")
            return False

def test_checkpoint_overwriting():
    """Test přepisování checkpoint."""
    print("\n🧪 Testování přepisování checkpoint...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        model_dir = os.path.join(temp_dir, "test_model")
        os.makedirs(model_dir, exist_ok=True)
        
        checkpoint_path = os.path.join(model_dir, "latest_checkpoint")
        config_file = os.path.join(checkpoint_path, "config.json")
        
        # První iterace
        os.makedirs(checkpoint_path, exist_ok=True)
        with open(config_file, 'w') as f:
            f.write('{"iteration": 1, "data": "first"}')
        
        with open(config_file, 'r') as f:
            first_content = f.read()
        print(f"   Po 1. iteraci: {first_content}")
        
        # Druhá iterace (přepíše)
        with open(config_file, 'w') as f:
            f.write('{"iteration": 2, "data": "second"}')
        
        with open(config_file, 'r') as f:
            second_content = f.read()
        print(f"   Po 2. iteraci: {second_content}")
        
        # Třetí iterace (přepíše znovu)
        with open(config_file, 'w') as f:
            f.write('{"iteration": 3, "data": "third"}')
        
        with open(config_file, 'r') as f:
            third_content = f.read()
        print(f"   Po 3. iteraci: {third_content}")
        
        # Ověříme, že obsah se změnil
        if "third" in third_content and "first" not in third_content and "second" not in third_content:
            print("✅ Přepisování checkpoint funguje správně!")
            return True
        else:
            print("❌ Problém s přepisováním checkpoint!")
            return False

def test_disk_space_usage():
    """Test využití diskového prostoru."""
    print("\n🧪 Testování využití diskového prostoru...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        model_dir = os.path.join(temp_dir, "test_model")
        os.makedirs(model_dir, exist_ok=True)
        
        # Simulujeme starý systém (verzované checkpointy)
        print("   Starý systém (verzované checkpointy):")
        old_system_dir = os.path.join(temp_dir, "old_system")
        os.makedirs(old_system_dir, exist_ok=True)
        
        old_total_size = 0
        for iteration in range(1, 6):  # 5 iterací
            iteration_dir = os.path.join(old_system_dir, f"iteration_{iteration}")
            os.makedirs(iteration_dir, exist_ok=True)
            
            # Vytvoříme soubory
            config_file = os.path.join(iteration_dir, "config.json")
            model_file = os.path.join(iteration_dir, "pytorch_model.bin")
            
            with open(config_file, 'w') as f:
                f.write('{"model_type": "test"}' * 100)  # ~2KB
            with open(model_file, 'w') as f:
                f.write('dummy model data' * 1000)  # ~15KB
            
            dir_size = sum(os.path.getsize(os.path.join(iteration_dir, f)) 
                          for f in os.listdir(iteration_dir))
            old_total_size += dir_size
            print(f"     iteration_{iteration}: {dir_size / 1024:.1f} KB")
        
        print(f"     Celková velikost: {old_total_size / 1024:.1f} KB")
        
        # Simulujeme nový systém (single checkpoint)
        print("   Nový systém (single checkpoint):")
        new_system_dir = os.path.join(temp_dir, "new_system")
        os.makedirs(new_system_dir, exist_ok=True)
        
        checkpoint_dir = os.path.join(new_system_dir, "latest_checkpoint")
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        # Vytvoříme pouze jeden checkpoint
        config_file = os.path.join(checkpoint_dir, "config.json")
        model_file = os.path.join(checkpoint_dir, "pytorch_model.bin")
        
        with open(config_file, 'w') as f:
            f.write('{"model_type": "test"}' * 100)  # ~2KB
        with open(model_file, 'w') as f:
            f.write('dummy model data' * 1000)  # ~15KB
        
        new_total_size = sum(os.path.getsize(os.path.join(checkpoint_dir, f)) 
                           for f in os.listdir(checkpoint_dir))
        
        print(f"     latest_checkpoint: {new_total_size / 1024:.1f} KB")
        print(f"     Celková velikost: {new_total_size / 1024:.1f} KB")
        
        # Porovnání
        savings = old_total_size - new_total_size
        savings_percent = (savings / old_total_size * 100) if old_total_size > 0 else 0
        
        print(f"   Úspora místa:")
        print(f"     Absolutní: {savings / 1024:.1f} KB")
        print(f"     Relativní: {savings_percent:.0f}%")
        
        if savings > 0:
            print("✅ Single checkpoint výrazně šetří místo na disku!")
            return True
        else:
            print("❌ Problém s výpočtem úspor!")
            return False

def test_checkpoint_cleanup():
    """Test čištění checkpoint."""
    print("\n🧪 Testování čištění checkpoint...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        model_dir = os.path.join(temp_dir, "test_model")
        os.makedirs(model_dir, exist_ok=True)
        
        checkpoint_path = os.path.join(model_dir, "latest_checkpoint")
        
        # Vytvoříme checkpoint
        os.makedirs(checkpoint_path, exist_ok=True)
        config_file = os.path.join(checkpoint_path, "config.json")
        with open(config_file, 'w') as f:
            f.write('{"model_type": "test"}')
        
        print(f"   Checkpoint vytvořen: {os.path.exists(checkpoint_path)}")
        
        # Simulujeme čištění (keep_checkpoint=False)
        if os.path.exists(checkpoint_path):
            shutil.rmtree(checkpoint_path)
            print("   Checkpoint smazán")
        
        # Ověříme, že byl smazán
        checkpoint_exists = os.path.exists(checkpoint_path)
        print(f"   Checkpoint existuje po čištění: {checkpoint_exists}")
        
        if not checkpoint_exists:
            print("✅ Čištění checkpoint funguje správně!")
            return True
        else:
            print("❌ Problém s čištěním checkpoint!")
            return False

if __name__ == "__main__":
    print("💾 Test single checkpoint systému pro Extended Fine-tuning")
    print("=" * 60)
    
    tests = [
        ("Single checkpoint creation", test_single_checkpoint_creation),
        ("Checkpoint overwriting", test_checkpoint_overwriting),
        ("Disk space usage", test_disk_space_usage),
        ("Checkpoint cleanup", test_checkpoint_cleanup)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🔍 {test_name}:")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PROŠEL")
            else:
                print(f"❌ {test_name} - SELHAL")
        except Exception as e:
            print(f"❌ {test_name} - CHYBA: {e}")
        print()
    
    print("=" * 60)
    print(f"📊 VÝSLEDKY: {passed}/{total} testů prošlo")
    
    if passed == total:
        print("🎉 Všechny testy single checkpoint systému prošly!")
        print("\n💡 Výhody single checkpoint:")
        print("   💾 Výrazná úspora diskového prostoru (80% méně)")
        print("   🔄 Jednodušší správa - pouze jeden checkpoint soubor")
        print("   🧹 Automatické přepisování starších verzí")
        print("   ⚡ Rychlejší operace s diskem")
        print("   🛡️  Stále poskytuje ochranu proti ztrátě pokroku")
    else:
        print("⚠️  Některé testy selhaly.")
