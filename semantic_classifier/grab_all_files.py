import os
import pandas as pd
from semantic_classifier.Classifier import Classifier
from OCR.OCRProcessor import OCRProcessor
from utils import utils

os.environ["TOKENIZERS_PARALLELISM"] = "false"

# Inicializace OCR procesoru
ocr_processor = OCRProcessor(language='ces', dpi=300, oem=1, psm=11)
classifier = Classifier()

# Cesta ke složce s PDF soubory
data_folder = '../Tests'
output_file = 'all_files_classification_results.csv'

# Seznam pro ukládání všech výsledků
all_results = []

print("🚀 Spouštím klasifikaci všech PDF souborů...")
print(f"📁 Hledám PDF soubory ve složce: {data_folder}")

# Najdeme všechny PDF soubory ve složce Data
pdf_files = []
if os.path.exists(data_folder):
    for file in os.listdir(data_folder):
        if file.lower().endswith('.pdf'):
            pdf_files.append(os.path.join(data_folder, file))

if not pdf_files:
    print(f"❌ CHYBA: Nenalezeny žádné PDF soubory ve složce {data_folder}")
    exit(1)

print(f"✓ Nalezeno {len(pdf_files)} PDF souborů")

# Zpracování každého PDF souboru
for i, file_path in enumerate(pdf_files, 1):
    filename = os.path.basename(file_path)
    print(f"\n📄 [{i}/{len(pdf_files)}] Zpracovávám: {filename}")

    try:
        # OCR zpracování
        print("   🔍 OCR zpracování...")
        ocr_processor.load_pdf(file_path)
        df = ocr_processor.get_items()
        print(f"   ✓ Nalezeno {len(df)} textových prvků")

        # Zpracování textů
        print("   📝 Zpracování textů...")
        df = utils.merge_texts(df)
        df = utils.clean_texts(df)
        df = utils.remove_invalid_texts(df)
        print(f"   ✓ Po zpracování: {len(df)} prvků")

        # Klasifikace klíčů
        print("   🔑 Klasifikace klíčů...")
        df = classifier.batch_classify(
            df=df,
            text_column='text',
            class_column='key_class',
            similarity_column='similarity',
            threshold=0.7,
            use_numeric_ids=False  # Používáme číselné identifikátory místo názvů kategorií
        )

        # Přidání názvu souboru a uložení výsledků
        for _, row in df.iterrows():
            if pd.notna(row.get('text', '')) and row.get('text', '').strip():
                result = {
                    'filename': filename,
                    'text': row['text'],
                    'key_class': row.get('key_class', 0),
                    'similarity': row.get('similarity', 0.0)
                }
                all_results.append(result)

        classified_count = len(df[df.get('key_class', 0) > 0])
        print(f"   ✓ Klasifikováno {classified_count}/{len(df)} klíčů")

    except Exception as e:
        print(f"   ❌ CHYBA při zpracování {filename}: {str(e)}")
        continue

# Uložení všech výsledků do CSV
if all_results:
    results_df = pd.DataFrame(all_results)
    results_df = results_df[results_df['similarity'] > 0.7]
    results_df.to_csv(output_file, index=False, encoding='utf-8')
    print(f"\n✅ DOKONČENO!")
    print(f"📊 Celkem zpracováno {len(all_results)} textových prvků z {len(pdf_files)} souborů")
    print(f"💾 Výsledky uloženy do: {output_file}")

    # Statistiky
    # classified_total = len(results_df[results_df['key_class'] > 0])
    # print(f"🎯 Celkem klasifikováno: {classified_total}/{len(all_results)} prvků")

    # if classified_total > 0:
    #     print(f"📈 Distribuce tříd:")
    #     class_counts = results_df[results_df['key_class'] > 0]['key_class'].value_counts().sort_index()
    #     for class_id, count in class_counts.items():
    #         print(f"   Třída {class_id}: {count} prvků")
else:
    print("❌ CHYBA: Žádné výsledky k uložení!")
