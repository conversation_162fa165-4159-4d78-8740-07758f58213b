from utils.utils import KEY_CLASS_REGISTRY


def do(df):
    import os
    import sys
    from semantic_classifier.Classifier import Classifier

    # Najdeme správnou cestu k modelu
    current_dir = os.getcwd()
    model_path = os.path.join(current_dir, 'semantic_classifier','mpnet')

    # Kontrola existence modelu
    if not os.path.exists(model_path):
        print(f"   ❌ CHYBA: Model nenalezen v cestě: {model_path}")
        print("   ❌ Key classification nelze provést bez natrénovaného modelu!")
        sys.exit(1)

    print(f"   ✓ Model nalezen v: {model_path}")

    # Inicializace klasifikátoru (bez načítání externího modelu)
    classifier = Classifier(model_path=model_path)
    #classifier.set_category_id_mapping(KEY_CLASS_REGISTRY)
    # Načtení mapování kategorií z Excel souboru
    print("   Loading category mapping from Excel...")

    # Klasifika<PERSON> textů s číselnými identifikátory
    print("   Classifying texts...")
    df = classifier.batch_classify(
        df=df,
        text_column='text',
        class_column='key_class',
        similarity_column='similarity',
        threshold=0.85,
        use_numeric_ids=True  # Používáme číselné identifikátory místo názvů kategorií
    )

    return df