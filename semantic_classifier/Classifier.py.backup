import torch
from torch.utils.data import DataLoader, random_split
from sentence_transformers import SentenceTransformer, InputExample, losses
from sentence_transformers.evaluation import EmbeddingSimilarityEvaluator, TripletEvaluator
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import os
import pandas as pd
import pickle
import re
import gc
import sys
import unicodedata
from typing import Callable, List, Dict, Tuple, Optional, Union
from tqdm.autonotebook import tqdm

class Classifier:
    """
    Klasifikátor frází z obchodních dokumentů pomocí sentence transformer modelu
    s klasifikací založenou na centrocích kategorií.

    Umožňuje fine-tuning modelu na datech typu kotva-pozitivní-negativní
    načtených z CSV souborů. Po tréninku/načtení modelu se vypočítají
    centroidy pro definované kategorie. Klasifikace pak probíhá porovnáním
    vstupní fráze s těmito centroidy. Model lze uložit ve formátu ONNX pro použití v C++.

    Obsahuje diagnostické funkce pro řešení problémů s grad_norm = 0.0:
    - _check_model_parameters(): Zkontroluje stav parametrů modelu
    - _ensure_model_trainable(): Odemkne parametry modelu
    - _force_unfreeze_all_layers(): Agresivnější odemknutí parametrů
    - check_gradients_after_step(): Kontrola gradientů po tréninku
    """

    def __init__(self, model_name):
        """
        Inicializuje klasifikátor načtením sentence transformer modelu.
        Nejprve zkontroluje, zda existuje lokální verze modelu, a pokud ano, načte ji.
        Pouze pokud lokální model neexistuje, načte model z externích zdrojů (Hugging Face Hub).

        Args:
            model_name (str): Název modelu nebo cesta k lokálnímu modelu.
                              Povinný parametr - musí být explicitně zadán.
        """
        if not model_name:
            raise ValueError("Název modelu (model_name) je povinný parametr a musí být explicitně zadán.")

        # Nastavení device na 'cuda' pokud je k dispozici GPU, jinak 'cpu'
        if torch.cuda.is_available():
            device = 'cuda'
            # Zkontroluje dostupnost MPX (pro Apple Silicon M1, M2, M3...)
        elif torch.backends.mps.is_built() and torch.backends.mps.is_available():
            device = 'mps'
            # MPS je nyní aktivní pro Apple Silicon (M1, M2, M3...)
            print("Používám Apple Metal Performance Accelerators (MPS)")
            # Pokud není k dispozici ani jedno, použije CPU
        else:
            device = 'cpu'

        self.device = device
        self.model_name = model_name  # Uložíme název modelu pro pozdější použití
        self.category_centroids = None

        # Mapování názvů kategorií na číselné identifikátory
        # Bude načteno z Excel souboru během fine-tuningu nebo explicitně nastaveno
        self.category_id_mapping = {}

        # Vytvoříme výchozí název složky pro ukládání modelu
        # Odstraníme znaky, které nejsou vhodné pro název složky
        self.model_dir = self._get_safe_model_dir_name(model_name)

        # PRIORITNÍ NAČÍTÁNÍ: Nejprve zkontrolujeme lokální model
        local_model_loaded = False

        # Zkontrolujeme, zda existuje lokální model v očekávané složce
        if self.check_model_exists(self.model_dir):
            print(f"Nalezen lokální model v: {self.model_dir}")
            print("Načítám lokální model (bez přístupu k externím zdrojům)...")
            local_model_loaded = self._load_local_model_only(self.model_dir)

        # Pokud se nepodařilo načíst lokální model, načteme z externích zdrojů
        if not local_model_loaded:
            if self.check_model_exists(self.model_dir):
                print("Nepodařilo se načíst lokální model, přepínám na externí zdroje...")
            else:
                print(f"Lokální model v {self.model_dir} nenalezen.")

            print(f"Načítám model z externích zdrojů: {model_name}...")
            self.model = SentenceTransformer(model_name, device=self.device)
            print(f"Model načten z externích zdrojů a běží na zařízení: {self.device}")

        print(f"Inicializace klasifikátoru dokončena (zařízení: {self.device})")

    def set_category_id_mapping(self, mapping):
        """
        Nastaví mapování názvů kategorií na číselné identifikátory.

        Poznámka: Mapování se automaticky načítá z Excel souboru během fine-tuningu.
        Tato metoda je určena pro explicitní nastavení mapování bez fine-tuningu.

        Args:
            mapping (dict): Slovník mapující názvy kategorií (str) na číselné identifikátory (int).
                           Například: {"Faktura": 1, "Datum": 2, "Částka": 3}
                           Doporučuje se použít utils.load_key_class_mapping_from_xlsx()
        """
        if not isinstance(mapping, dict):
            raise ValueError("Mapování musí být slovník (dict).")

        # Ověříme, že všechny hodnoty jsou číselné
        for category_name, category_id in mapping.items():
            if not isinstance(category_id, int):
                raise ValueError(f"Identifikátor kategorie '{category_name}' musí být celé číslo, ale je {type(category_id)}.")

        self.category_id_mapping = mapping.copy()
        print(f"Nastaveno mapování kategorií pro {len(mapping)} kategorií.")

    def get_category_id(self, category_name):
        """
        Vrátí číselný identifikátor pro daný název kategorie.

        Args:
            category_name (str): Název kategorie.

        Returns:
            int: Číselný identifikátor kategorie nebo 0 pro neznámou kategorii.
        """
        if category_name is None:
            return 0

        if not self.category_id_mapping:
            print("Upozornění: Mapování kategorií není nastaveno. Použijte fine_tune() nebo set_category_id_mapping().")
            return 0

        return self.category_id_mapping.get(category_name, 0)

    def preprocess(self, text):
        """
        Preprocessing funkce pro normalizaci textů před jejich zpracováním.
        Aplikuje se na všechny texty před kódováním do embeddingů.

        Args:
            text (str): Vstupní text k preprocessing.

        Returns:
            str: Preprocessovaný text.
        """
        if not isinstance(text, str):
            return str(text) if text is not None else ""

        #text = unicodedata.normalize("NFC", text)              # Normalize Unicode
        #text = text.lower().strip()                            # Lowercase
        text = re.sub(r'(?<=\w)\.(?=\w)', ' ', text)           # Replace dots between letters with space
        text = re.sub(r'[,:;]', '', text)                     # Remove remaining punctuation
        text = re.sub(r'\s+', ' ', text).strip()               # Collapse multiple spaces
        return text

    def _get_safe_model_dir_name(self, model_name):
        """
        Vytvoří bezpečný název složky z názvu modelu.

        Args:
            model_name (str): Název modelu.

        Returns:
            str: Bezpečný název složky.
        """
        # Nahradíme znaky, které nejsou vhodné pro název složky
        safe_name = re.sub(r'[^a-zA-Z0-9_-]', '_', model_name)
        return f"model_{safe_name}"

    def _manage_memory(self):
        """
        Pomocná metoda pro správu paměti pro všechna zařízení.
        Pokusí se uvolnit nepoužívanou paměť.
        """
        # Vynucení garbage collection
        gc.collect()

        # Vyčištění cache pro všechny dostupné backendy
        if self.device == 'mps':
            # Pro MPS zařízení (Apple Silicon)
            if hasattr(torch, 'mps') and hasattr(torch.mps, 'empty_cache'):
                torch.mps.empty_cache()
        elif self.device == 'cuda':
            # Pro CUDA zařízení
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

        # Další kolo garbage collection po vyčištění cache
        gc.collect()

    def encode(self, texts, batch_size=16):
        """
        Generuje embeddingy pro seznam textů.
        Automaticky aplikuje preprocessing na všechny texty před kódováním.

        Args:
            texts (list): Seznam textových řetězců k zakódování.
            batch_size (int): Velikost dávky pro zpracování.

        Returns:
            numpy.ndarray: Pole embeddingů.
        """
        # Používáme model.encode pro generování embeddingů.
        # convert_to_numpy=True zajistí, že výstup bude NumPy pole.
        # device je nastaveno v __init__
        if not isinstance(texts, list):
             texts = [texts] # Zajistíme, že vstup je seznam, i když je to jen jedna fráze

        # Aplikujeme preprocessing na všechny texty
        preprocessed_texts = [self.preprocess(text) for text in texts]

        # Adjust batch size for MPS to avoid memory issues
        actual_batch_size = batch_size
        if self.device == 'mps':
            # Use a smaller batch size on MPS to avoid memory issues
            actual_batch_size = min(batch_size, 16)  # Limit batch size on MPS
            if len(preprocessed_texts) > actual_batch_size and batch_size > actual_batch_size:
                print(f"Using reduced batch size {actual_batch_size} for encoding on MPS device (original: {batch_size})")

        return self.model.encode(preprocessed_texts, convert_to_numpy=True, batch_size=actual_batch_size, show_progress_bar=False)

    def load_training_data_from_xlsx(self, data_path):
        """
        Načte tréninková data z XLSX souboru s novou strukturou.
        Očekává jeden list pro každou kategorii.
        Struktura listu:
        A1: TEXT KOTVY PRO TRIPLET LOSS (popisná věta / prefix)
        A2: (ignorováno)
        B1: Hlavička (Pozitivní varianty)
        B2 a dál: TEXTY POZITIVNÍCH VARIANT
        C1: Hlavička (Negativní příklady)
        C2 a dál: TEXTY NEGATIVNÍCH PŘÍKLADŮ

        Args:
            data_path (str): Cesta k XLSX souboru nebo adresáři obsahujícímu soubor training_set.xlsx.

        Returns:
            list: Seznam slovníků ve formátu [{'anchor_triplet': '...', 'positives_variants': [...], 'negatives': [...]}, ...]
        """
        # Pokud je data_path adresář, přidáme název souboru
        if os.path.isdir(data_path):
            data_path = os.path.join(data_path, 'training_set.xlsx')

        training_data = []
        print(f"Načítám tréninková data ze souboru: {data_path} s novou strukturou.")

        if not os.path.exists(data_path):
            print(f"Chyba: Soubor '{data_path}' nenalezen.")
            return []

        try:
            # Načtení všech listů z XLSX souboru
            xlsx = pd.ExcelFile(data_path)
            sheet_names = xlsx.sheet_names

            if not sheet_names:
                print(f"Chyba: Soubor '{data_path}' neobsahuje žádné listy.")
                return []

            print(f"Nalezeno {len(sheet_names)} listů v souboru.")

            # Zpracování každého listu
            for sheet_name in sheet_names:
                try:
                    # Načtení listu do DataFrame
                    # Použijeme header=None, abychom neztratili první řádek jako hlavičku
                    df = pd.read_excel(xlsx, sheet_name=sheet_name, header=None, dtype=str).replace({np.nan: None})

                    # Kontrola, zda DataFrame obsahuje data a alespoň 3 sloupce
                    if df.empty or len(df.columns) < 3:
                        print(
                            f"    Upozornění: List '{sheet_name}' neobsahuje dostatek sloupců nebo dat. Očekávány jsou 3 sloupce A, B, C. Přeskakuji.")
                        continue

                    # Získání textu kotvy pro TripletLoss z buňky A2 (řádek 1, sloupec 0)
                    # Ignorujeme první řádek, který je hlavička "Kotva"
                    anchor_triplet_text = str(df.iloc[1, 0]).strip() if pd.notna(df.iloc[1, 0]) else None

                    if not anchor_triplet_text:
                        print(f"    Upozornění: List '{sheet_name}' neobsahuje text kotvy v buňce A2. Přeskakuji.")
                        continue

                    # Aplikujeme preprocessing na kotvu
                    anchor_triplet_text = self.preprocess(anchor_triplet_text)

                    # Získání pozitivních variant ze sloupce B (od řádku 1, tj. druhý řádek v Excelu)
                    # Ignorujeme první řádek, který je hlavička "Pozitivní varianty"
                    positives_variants = [self.preprocess(str(val).strip()) for val in df.iloc[1:, 1].tolist() if
                                          pd.notna(val) and str(val).strip() != '']

                    # Získání negativních příkladů ze sloupce C (od řádku 1, tj. druhý řádek v Excelu)
                    # Ignorujeme první řádek, který je hlavička "Negativní příklady"
                    negatives = [self.preprocess(str(val).strip()) for val in df.iloc[1:, 2].tolist() if
                                 pd.notna(val) and str(val).strip() != '']

                    # Pro TripletLoss potřebujeme alespoň jednu pozitivní variantu a jeden negativní příklad
                    # Kotvu pro TripletLoss už máme (anchor_triplet_text)
                    if not positives_variants or not negatives:
                        print(
                            f"    Upozornění: List '{sheet_name}' nemá dostatek pozitivních variant nebo negativních příkladů (alespoň jeden od každého typu). Přeskakuji.")
                        continue

                    training_data.append({
                        'category_name': sheet_name,  # Název kategorie je název listu
                        'anchor_triplet': anchor_triplet_text,  # Text kotvy pro TripletLoss z A1
                        'positives_variants': positives_variants,  # Seznam pozitivních variant z B2 a dál
                        'negatives': negatives  # Seznam negativních příkladů z C2 a dál
                    })

                    print(
                        f"    Načteno z listu '{sheet_name}': Kategorie='{sheet_name}', Kotva pro triplet='{anchor_triplet_text}', {len(positives_variants)} pozitivních variant, {len(negatives)} negativních.")

                except Exception as e:
                    print(f"    Chyba při zpracování listu '{sheet_name}': {e}")

        except Exception as e:
            print(f"Chyba při načítání souboru '{data_path}': {e}")

        print(f"Načítání dat dokončeno. Celkem {len(training_data)} sad dat pro triplet trénink.")
        # structured_data teď bude seznam těchto slovníků
        return training_data

    def prepare_training_data(self, structured_data, print_triplets=False):
        """
        Připraví tréninková data z formátu
        {'anchor_triplet': '...', 'positives_variants': [...], 'negatives': [...]}
        na seznam InputExample pro TripletLoss.

        Optimalizovaná verze pro efektivní využití negativních příkladů:
        - Kombinuje anchor s každým positive (vzájemně)
        - Kombinuje každý positive s každým jiným positive navzájem
        - K těmto kombinacím přiřazuje negativní příklady postupně (cyklicky)
        - Eliminuje zbytečné opakování stejných kotev a pozitivních s různými negativními

        Args:
            structured_data (list): Seznam slovníků ve formátu
                                  {'anchor_triplet': '...', 'positives_variants': [...], 'negatives': [...]}.
            print_triplets (bool): Pokud True, vypíše všechny vytvořené triplety pro kontrolu tréninku.

        Returns:
            list: Seznam objektů sentence_transformers.InputExample.
        """
        examples = []
        triplet_count = 0

        if print_triplets:
            print("\n=== VÝPIS TRIPLETŮ POUŽITÝCH PRO TRÉNINK (OPTIMALIZOVANÁ VERZE) ===")

        for item in structured_data:
            # Vezmeme kotvu, pozitivní varianty a negativní příklady
            anchor_for_triplet = item['anchor_triplet']
            positives_for_triplet = item['positives_variants']
            negatives_for_triplet = item['negatives']
            category_name = item.get('category_name', 'Neznámá kategorie')

            # Již jsme kontrolovali v load_training_data_from_xlsx,
            # že máme alespoň jednoho pozitiva a jednoho negativa pro triplet.
            # Nicméně, pokud by logika načítání byla jiná, je dobré tuto kontrolu nechat.
            if not positives_for_triplet or not negatives_for_triplet:
                if print_triplets:
                    print(f"    Upozornění: Kotva '{anchor_for_triplet}' nemá dostatek pozitivních nebo negativních příkladů pro TripletLoss. Přeskakuji triplet.")
                continue

            if print_triplets:
                print(f"\nKategorie: {category_name}")
                print(f"Kotva: '{anchor_for_triplet}'")
                print(f"Pozitivní varianty ({len(positives_for_triplet)}): {positives_for_triplet}")
                print(f"Negativní příklady ({len(negatives_for_triplet)}): {negatives_for_triplet}")

            # Vytvoříme všechny pozitivní varianty (anchor + positives)
            all_positives = [anchor_for_triplet] + positives_for_triplet

            # Vytvoříme všechny kombinace pozitivních navzájem (bez opakování se sebou samým)
            positive_pairs = []
            for i, positive1 in enumerate(all_positives):
                for j, positive2 in enumerate(all_positives):
                    # Přeskočíme kombinace se sebou samým
                    if i == j:
                        continue
                    positive_pairs.append((positive1, positive2))

            if print_triplets:
                print(f"Všechny pozitivní (kotva + varianty) ({len(all_positives)}): {all_positives}")
                print(f"Kombinace pozitivních párů: {len(positive_pairs)}")
                print("Vytvořené triplety (optimalizované přiřazení negativních):")

            # Přiřadíme negativní příklady cyklicky k pozitivním párům
            negative_index = 0
            for pair_index, (positive1, positive2) in enumerate(positive_pairs):
                # Vybereme negativní příklad cyklicky
                negative_example = negatives_for_triplet[negative_index % len(negatives_for_triplet)]
                negative_index += 1

                triplet_count += 1
                examples.append(InputExample(texts=[positive1, positive2, negative_example]))

                if print_triplets:
                    print(f"  {triplet_count:3d}. ['{positive1}', '{positive2}', '{negative_example}']")

        if print_triplets:
            print(f"\n=== KONEC VÝPISU TRIPLETŮ ===")
            print(f"Celkem vytvořeno {len(examples)} tripletů z {len(structured_data)} kategorií.")
            print(f"Negativní příklady jsou přiřazeny cyklicky k pozitivním párům.")
        else:
            print(f"Připraveno {len(examples)} tréninkových tripletů pro TripletLoss (optimalizovaná verze).")

        # Zde byste mohli přidat kód pro rozdělení na trénovací a validační sadu,
        # pokud ho nebudete dělat dříve.
        return examples

    def calculate_category_centroids(self, structured_data, save_path=None):
        """
        Vypočítá a uloží centroidy pro každou sémantickou kategorii
        na základě průměru embeddingů kotevní fráze a jejích pozitivních příkladů.

        Args:
            structured_data (list): Seznam slovníků ve formátu
                                  {'category_name': '...', 'anchor_triplet': '...', 'positives_variants': [...], 'negatives': [...]}.
                                  Toto jsou data načtená metodou load_training_data_from_xlsx.
            save_path (str, optional): Cesta pro uložení vypočtených centroidů. Pokud None, centroidy se neuloží na disk.
        """
        print("Vypočítávám centroidy kategorií...")
        if not structured_data:
            print("Nenalezena strukturovaná data pro výpočet centroidů.")
            self.category_centroids = None
            return

        # Shromáždíme všechny fráze a vytvoříme mapování indexů na kategorie
        all_phrases_for_encoding = []
        category_index_map = {}
        current_embedding_index = 0

        for item in structured_data:
            # Použijeme název listu jako název kategorie
            category_name = item['category_name']
            # Použijeme anchor_triplet a positives_variants jako fráze pro výpočet centroidu
            phrases = [item['anchor_triplet']] + item['positives_variants']

            if not phrases:
                print(f"    Upozornění: Kategorie '{category_name}' nemá žádné fráze (kotva+pozitivní) pro výpočet centroidu. Přeskakuji.")
                continue

            category_index_map[category_name] = (current_embedding_index, current_embedding_index + len(phrases))
            all_phrases_for_encoding.extend(phrases)
            current_embedding_index += len(phrases)

        if not all_phrases_for_encoding:
            print("Nenalezeny žádné fráze pro výpočet centroidů.")
            self.category_centroids = None
            return

        # Zakódujeme všechny fráze najednou
        print(f"  Zakódovávám {len(all_phrases_for_encoding)} frází pro výpočet centroidů...")
        # Use a more memory-efficient batch size, especially for MPS
        encode_batch_size = 16 if self.device == 'mps' else 64
        all_embeddings = self.encode(all_phrases_for_encoding, batch_size=encode_batch_size)
        print("  Kódování dokončeno.")

        # Uvolnění paměti po náročné operaci
        self._manage_memory()

        # Vypočítáme průměrné embeddingy (centroidy) pro každou kategorii podle mapování indexů
        self.category_centroids = {}
        for category_name, (start_idx, end_idx) in category_index_map.items():
            if start_idx >= end_idx:  # Prázdný rozsah
                continue

            category_embeddings = all_embeddings[start_idx:end_idx]
            # Vypočítáme průměr embeddingů podél osy 0 (průměr přes jednotlivé embedding vektory)
            centroid = np.mean(category_embeddings, axis=0)
            # Normalizujeme centroid, aby kosinusová podobnost fungovala správně
            centroid = centroid / np.linalg.norm(centroid)
            self.category_centroids[category_name] = centroid

        print(f"Výpočet centroidů dokončen pro {len(self.category_centroids)} kategorií.")

        # Uložení centroidů na disk, pokud je zadána cesta
        if save_path and self.category_centroids:
            self.save_centroids(save_path)

    def save_centroids(self, save_path):
        """
        Uloží vypočtené centroidy kategorií do souboru pro pozdější použití.

        Args:
            save_path (str): Cesta pro uložení centroidů. Může být cesta k souboru nebo adresáři.
                             Pokud je to adresář, soubor se vytvoří jako 'centroids.pkl' v tomto adresáři.
        """
        if self.category_centroids is None or not self.category_centroids:
            print("Chyba: Centroidy kategorií nejsou vypočítány. Nelze uložit.")
            return

        # Kontrola, zda je save_path adresář nebo soubor
        if os.path.isdir(save_path) or save_path.endswith('/'):
            os.makedirs(save_path, exist_ok=True)
            save_path = os.path.join(save_path, 'centroids.pkl')
        else:
            # Vytvoření adresáře, pokud neexistuje
            os.makedirs(os.path.dirname(os.path.abspath(save_path)), exist_ok=True)

        try:
            # Uložíme jak centroidy, tak mapování kategorií
            data_to_save = {
                'centroids': self.category_centroids,
                'category_id_mapping': self.category_id_mapping
            }
            with open(save_path, 'wb') as f:
                pickle.dump(data_to_save, f)
            print(f"Centroidy a mapování kategorií úspěšně uloženy do: {save_path}")
        except Exception as e:
            print(f"Chyba při ukládání centroidů: {str(e)}")

    def load_centroids(self, load_path):
        """
        Načte centroidy kategorií ze souboru.

        Args:
            load_path (str): Cesta k souboru s uloženými centroidy.

        Returns:
            bool: True pokud se centroidy úspěšně načetly, jinak False.
        """
        try:
            # Kontrola, zda je load_path adresář nebo soubor
            if os.path.isdir(load_path):
                load_path = os.path.join(load_path, 'centroids.pkl')

            if not os.path.exists(load_path):
                print(f"Chyba: Soubor s centroidy '{load_path}' neexistuje.")
                return False

            with open(load_path, 'rb') as f:
                loaded_data = pickle.load(f)

            # Kontrola, zda je to nový formát s mapováním nebo starý formát pouze s centroidy
            if isinstance(loaded_data, dict) and 'centroids' in loaded_data:
                # Nový formát - obsahuje centroidy i mapování
                self.category_centroids = loaded_data['centroids']
                if 'category_id_mapping' in loaded_data:
                    self.category_id_mapping = loaded_data['category_id_mapping']
                    print(f"Načteno mapování pro {len(self.category_id_mapping)} kategorií.")
                else:
                    print("Mapování kategorií nebylo nalezeno v souboru, používám výchozí.")
            else:
                # Starý formát - pouze centroidy
                self.category_centroids = loaded_data
                print("Načten starý formát centroidů bez mapování kategorií, používám výchozí mapování.")

            print(f"Centroidy úspěšně načteny z: {load_path}")
            print(f"Načteno {len(self.category_centroids)} kategorií.")
            return True
        except Exception as e:
            print(f"Chyba při načítání centroidů: {str(e)}")
            self.category_centroids = None
            return False


    def check_model_exists(self, model_dir):
        """
        Zkontroluje, zda existuje lokální uložený model.

        Args:
            model_dir (str): Cesta k adresáři, kde by měl být uložen model.

        Returns:
            bool: True pokud model existuje, jinak False.
        """
        # Kontrola, zda adresář existuje
        if not os.path.isdir(model_dir):
            print(f"Adresář {model_dir} neexistuje.")
            return False

        # Kontrola, zda adresář obsahuje základní soubory modelu
        # SentenceTransformer model by měl obsahovat alespoň config.json
        config_file = os.path.join(model_dir, 'config.json')
        if not os.path.exists(config_file):
            print(f"Soubor config.json nebyl nalezen v {model_dir}.")
            return False

        # Kontrola, zda existuje modules.json nebo nějaký modul
        # Různé verze SentenceTransformer mohou mít různou strukturu
        modules_file = os.path.join(model_dir, 'modules.json')
        if os.path.exists(modules_file):
            # Pokud existuje modules.json, považujeme model za platný
            return True

        # Pokud neexistuje modules.json, zkontrolujeme, zda existuje nějaký modul
        # Např. 0_Transformer, 1_Pooling, atd.
        for i in range(5):  # Kontrolujeme moduly 0 až 4
            module_dir = os.path.join(model_dir, f"{i}_")
            # Hledáme adresáře začínající "{i}_"
            for item in os.listdir(model_dir):
                item_path = os.path.join(model_dir, item)
                if os.path.isdir(item_path) and item.startswith(f"{i}_"):
                    return True

        # Pokud jsme nenalezli žádný modul, zkontrolujeme, zda existuje pytorch_model.bin
        # což je také indikátor platného modelu
        model_bin = os.path.join(model_dir, 'pytorch_model.bin')
        if os.path.exists(model_bin):
            return True

        print(f"V adresáři {model_dir} nebyl nalezen žádný platný model.")
        return False

    def _load_local_model_only(self, model_dir):
        """
        Načte model pouze z lokálního adresáře bez přístupu k externím zdrojům.
        Tato metoda je určena pro použití v konstruktoru, aby se zabránilo
        nechtěnému přístupu k externím zdrojům, pokud existuje lokální model.

        Args:
            model_dir (str): Cesta k adresáři s lokálním modelem.

        Returns:
            bool: True pokud se model úspěšně načetl, jinak False.
        """
        try:
            print(f"Načítám lokální model ze složky: {model_dir}")
            self.model = SentenceTransformer(model_dir, device=self.device)

            # Získání informací o modelu - bezpečné získání informací
            try:
                dimension = self.model.get_sentence_embedding_dimension()

                # Pokus o získání názvu modelu - různé verze SentenceTransformer mohou mít různou strukturu
                model_name = "lokální model"
                try:
                    if hasattr(self.model, '_modules') and '0' in self.model._modules:
                        module_0 = self.model._modules['0']
                        if hasattr(module_0, 'auto_model') and hasattr(module_0.auto_model, 'config'):
                            model_name = getattr(module_0.auto_model.config, '_name_or_path', 'lokální model')
                except:
                    pass  # Ignorujeme chyby při získávání názvu modelu

                print(f"Lokální model úspěšně načten (dimenze: {dimension}, zařízení: {self.device}, model: {model_name})")
            except Exception as e:
                # Pokud se nepodaří získat informace o modelu, pouze to zaznamenáme, ale pokračujeme
                print(f"Upozornění: Nepodařilo se získat všechny informace o lokálním modelu: {str(e)}")
                print("Lokální model byl načten, ale některé informace nejsou dostupné.")

            # Pokus o načtení centroidů, pokud jsou k dispozici
            centroids_path = os.path.join(model_dir, 'centroids.pkl')
            if os.path.exists(centroids_path):
                success = self.load_centroids(centroids_path)
                if success:
                    print("Centroidy byly automaticky načteny s lokálním modelem.")
                else:
                    print("Centroidy se nepodařilo načíst, ale lokální model byl načten úspěšně.")
            else:
                print("Soubor s centroidy nebyl nalezen v lokálním modelu.")

            return True

        except Exception as e:
            print(f"Chyba při načítání lokálního modelu z '{model_dir}': {e}")
            return False

    def _check_model_parameters(self):
        """
        Diagnostická funkce pro kontrolu stavu parametrů modelu.
        Zkontroluje, které parametry jsou trainable a které jsou zamčené.
        """
        print("\n=== DIAGNOSTIKA PARAMETRŮ MODELU ===")

        total_params = 0
        trainable_params = 0
        frozen_params = 0

        # Projdeme všechny parametry modelu
        for name, param in self.model.named_parameters():
            total_params += param.numel()
            if param.requires_grad:
                trainable_params += param.numel()
                print(f"✓ TRAINABLE: {name} - {param.numel():,} parametrů")
            else:
                frozen_params += param.numel()
                print(f"✗ FROZEN:    {name} - {param.numel():,} parametrů")

        print(f"\n--- SOUHRN ---")
        print(f"Celkem parametrů:     {total_params:,}")
        print(f"Trainable parametrů:  {trainable_params:,} ({100*trainable_params/total_params:.1f}%)")
        print(f"Frozen parametrů:     {frozen_params:,} ({100*frozen_params/total_params:.1f}%)")

        # Kontrola training mode
        print(f"\nModel training mode: {self.model.training}")
        for name, module in self.model.named_modules():
            if hasattr(module, 'training'):
                print(f"  {name}: training={module.training}")

        print("=== KONEC DIAGNOSTIKY ===\n")

        return trainable_params > 0



    def fine_tune(self, data_dir, model_dir=None, num_epochs=5, train_batch_size=16, learning_rate=2e-5, save_centroids_path=None, validation_split=0.2, print_triplets=False):
        """
        Provede fine-tuning modelu na datech načtených z XLSX souborů v zadaném adresáři.
        Použije lokální model, pokud existuje, jinak výchozí.
        Implementuje Early Stopping pomocí Evaluatoru a save_best_model.

        Args:
            data_dir (str): Cesta k adresáři obsahujícímu XLSX soubor s tréninkovými daty.
            model_dir (str): Cesta k adresáři, kde je/bude uložen nejlepší model.
            num_epochs (int): Maximální počet epoch pro trénink (Early Stopping může zastavit dříve).
            train_batch_size (int): Velikost dávky pro trénink i validaci.
            learning_rate (float): Učící rychlost.
            save_centroids_path (str, optional): Cesta pro uložení vypočtených centroidů. Pokud None, použije se model_dir.
            validation_split (float): Podíl připravených tripletů, který bude použit pro validaci (0.0 až 1.0).
            print_triplets (bool): Pokud True, vypíše všechny vytvořené triplety pro kontrolu tréninku.

        Returns:
             list: Strukturovaná data načtená z XLSX souborů.
        """
        # Pokud není zadán model_dir, použijeme výchozí název složky odvozený od názvu modelu
        if model_dir is None:
            model_dir = self.model_dir

        # Vytvoříme adresář pro model, pokud neexistuje - je potřeba pro save_best_model=True
        os.makedirs(model_dir, exist_ok=True)

        # Pokud není zadána cesta pro uložení centroidů, použijeme model_dir
        if save_centroids_path is None:
            save_centroids_path = os.path.join(model_dir, "centroids")

        # Kontrola, zda potřebujeme načíst jiný lokální model než ten z konstruktoru
        # Pokud model_dir odpovídá self.model_dir, model už byl načten v konstruktoru
        if model_dir != self.model_dir and self.check_model_exists(model_dir):
            print(f"Nalezen jiný lokální model v: {model_dir}")
            print("Načítám specifikovaný model pro fine-tuning...")
            # Načtení lokálního modelu - fit bude trénovat DÁL na tomto modelu
            model_loaded = self.load_model(model_dir, load_centroids=False) # Nenačítáme centroidy, ty přepočítáme
            if not model_loaded:
                print("Nepodařilo se načíst specifikovaný lokální model. Použiji aktuální model z instance.")
        else:
            if model_dir == self.model_dir:
                print("Používám model načtený v konstruktoru pro fine-tuning.")
            else:
                print(f"Lokální model v: {model_dir} nenalezen. Používám aktuální model z instance.")

        print("Spouštím fine-tuning...")

        # DIAGNOSTIKA: Zkontrolujeme stav parametrů modelu před tréninkem
        has_trainable_params = self._check_model_parameters()

        if not has_trainable_params:
            print("⚠️  VAROVÁNÍ: Model nemá žádné trainable parametry!")
            print("Pokusím se odemknout parametry...")
            self._ensure_model_trainable()
            # Znovu zkontrolujeme
            has_trainable_params = self._check_model_parameters()

            if not has_trainable_params:
                print("⚠️  Základní metoda selhala, zkouším agresivnější přístup...")
                self._force_unfreeze_all_layers()
                # Finální kontrola
                has_trainable_params = self._check_model_parameters()

                if not has_trainable_params:
                    print("❌ CHYBA: Nepodařilo se odemknout parametry modelu!")
                    print("Fine-tuning bude neúčinný (grad_norm = 0.0)")
                    return []

        # Načteme data z XLSX souboru (s novou strukturou)
        structured_data = self.load_training_data_from_xlsx(data_dir)

        if not structured_data:
            print("Nenalezena žádná platná tréninková data. Fine-tuning zrušen.")
            return []

        # Připravíme data jako flat list tripletů [ankr, pozitivní, negativní]
        all_examples = self.prepare_training_data(structured_data, print_triplets=print_triplets) # Tato metoda už vrací triplety [popis/prefix, varianta, negativ]

        if not all_examples:
            print("Z tréninkových dat nebyly vytvořeny žádné triplety InputExample. Fine-tuning zrušen.")
            # I když není dost tripletů pro trénink, můžeme zkusit spočítat centroidy z načtených dat
            self.calculate_category_centroids(structured_data, save_centroids_path)
            return structured_data

        # Rozdělení dat na trénovací a validační sadu
        train_examples = all_examples
        val_examples = None
        val_dataloader = None
        evaluator = None

        if validation_split > 0 and validation_split < 1.0:
            print(f"Rozděluji data na trénovací a validační sadu (validační podíl: {validation_split})")
            val_size = int(len(all_examples) * validation_split)
            train_size = len(all_examples) - val_size

            if val_size == 0:
                 print("    Validační sada by měla 0 příkladů, Early Stopping nebude použit.")
                 train_examples = all_examples
                 val_examples = None # Explicitně nastavíme na None
            else:
                # Použijeme random_split pro rozdělení dat
                # Poznámka: random_split může vést k nerovnoměrnému rozložení kategorií v malé sadě
                train_examples, val_examples = random_split(
                    all_examples,
                    [train_size, val_size],
                    generator=torch.Generator().manual_seed(42)  # Pro reprodukovatelnost
                )
                print(f"Trénovací sada: {len(train_examples)} příkladů, Validační sada: {len(val_examples)} příkladů")
        else:
            train_examples = all_examples
            val_examples = None # Explicitně nastavíme na None
            print(f"Používám všechna data pro trénink: {len(train_examples)} příkladů. Early Stopping nebude použit.")


        # Set pin_memory=False explicitly for MPS compatibility
        # Reduce batch size for MPS to avoid memory issues
        actual_batch_size = train_batch_size
        if self.device == 'mps':
            # Use a smaller batch size on MPS to avoid memory issues
            actual_batch_size = min(train_batch_size, 16)  # Limit batch size on MPS
            print(f"Using reduced batch size {actual_batch_size} for MPS device (original: {train_batch_size})")
        elif self.device == 'cuda':
             # Můžete zde zkusit povolit mixed precision pro CUDA, pokud ji váš hardware podporuje
             print("Zvažte použití mixed precision training (FP16) pro úsporu paměti na CUDA.")


        train_dataloader = DataLoader(
            train_examples,
            shuffle=True,
            batch_size=actual_batch_size,
            pin_memory=(self.device != 'mps')
        )

        # Vytvoříme evaluator, pokud máme validační data
        if val_examples is not None:
            # Extrahujeme data z validačních příkladů pro TripletEvaluator
            # Pro triplet data potřebujeme kotvy, pozitivní a negativní příklady
            anchors = []
            positives = []
            negatives = []

            # Projdeme všechny validační příklady a extrahujeme data
            for example in val_examples:
                # InputExample má atribut texts, který obsahuje [kotva, pozitivní, negativní]
                anchors.append(example.texts[0])
                positives.append(example.texts[1])
                negatives.append(example.texts[2])

            # Vytvoříme TripletEvaluator, který správně vyhodnotí triplet loss trénink
            # Měří přesnost tripletů: zda je distance(anchor, positive) < distance(anchor, negative) + margin
            evaluator = TripletEvaluator(
                anchors=anchors,
                positives=positives,
                negatives=negatives,
                name="validation"
            )
            print(f"Připraven TripletEvaluator pro Early Stopping s {len(anchors)} triplety.")

        train_loss = losses.TripletLoss(model=self.model)

        # Nastavení pro model.fit
        fit_params = {
            'train_objectives': [(train_dataloader, train_loss)],
            'epochs': num_epochs,
            # Warmup steps by default is 10% of total training steps
            'warmup_steps': int(len(train_dataloader) * num_epochs * 0.1),
            'output_path': model_dir, # Zde se uloží NEJLEPŠÍ model, pokud je evaluator a save_best_model=True
            'save_best_model': True, # Povolíme ukládání nejlepšího modelu (aktivuje Early Stopping s Evaluator)
            'evaluator': evaluator, # Předáme připravený evaluátor
        }

        # Pokud nemáme validační sadu/evaluator, vypneme ukládání nejlepšího modelu (není podle čeho ho vybrat)
        if evaluator is None:
             fit_params['save_best_model'] = False
             print("Early Stopping není aktivní, trénink poběží po celý počet epoch.")

        # Finální kontrola před tréninkem
        print("\n=== FINÁLNÍ KONTROLA PŘED TRÉNINKEM ===")
        trainable_count = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        print(f"Trainable parametrů: {trainable_count:,}")
        print(f"Model training mode: {self.model.training}")
        print(f"Learning rate: {learning_rate}")
        print(f"Batch size: {actual_batch_size}")
        print("==========================================\n")

        # Spustíme trénink
        self.model.fit(**fit_params)

        print("Fine-tuning dokončen (nebo zastaven Early Stoppingem).")
        print(f"Výsledný model (nejlepší nebo poslední) je uložen v: {model_dir}")

        # Uvolnění paměti po tréninku
        self._manage_memory()

        # Důležité: Po dokončení fit, self.model INSTANCE nemusí nutně odkazovat
        # na ten NEJLEPŠÍ model, který byl uložen do model_dir.
        # Abychom spočítali centroidy s vahami NEJLEPŠÍHO modelu, musíme ho znovu načíst.
        print(f"Načítám nejlepší (nebo poslední) uložený model z {model_dir} pro výpočet centroidů.")
        model_loaded_for_centroids = self.load_model(model_dir, load_centroids=False)

        if model_loaded_for_centroids:
            # Nyní počítáme centroidy s načteným nejlepším/posledním modelem
            print("Počítám centroidy na základě trénovacích dat a výsledného modelu.")
            self.calculate_category_centroids(structured_data, save_centroids_path)

            # Uložíme model S centroidy
            self.save_model(model_dir, save_centroids=True) # Uloží model (již načtený nejlepší) a přiřadí centroidy
        else:
            print("Nepodařilo se znovu načíst výsledný model pro výpočet centroidů.")


        return structured_data # Vrátíme načtená data pro případné další použití


    def contains_digit(self, text):
        """
        Kontroluje, zda text obsahuje číslici.

        Args:
            text (str): Text ke kontrole.

        Returns:
            bool: True pokud text obsahuje číslici, jinak False.
        """
        return bool(re.search(r'\d', text))

    def classify(self, phrase, use_numeric_ids=False):
        """
        Klasifikuje vstupní frázi do jedné z předdefinovaných sémantických kategorií
        na základě nejvyšší kosinusové podobnosti s centroidy kategorií.
        Přeskakuje texty, které obsahují číslice.
        Automaticky aplikuje preprocessing na vstupní frázi.

        Args:
            phrase (str): Fráze k zařazení.
            use_numeric_ids (bool): Pokud True, vrátí číselný identifikátor kategorie místo názvu.

        Returns:
            tuple: Dvojice (kategorie, nejvyšší_podobnost) kde kategorie je buď název (str)
                   nebo číselný identifikátor (int) podle parametru use_numeric_ids.
                   Vrátí (None/0, -1.0) pokud centroidy nebyly vypočítány, podobnost je nízká,
                   nebo text obsahuje číslice.
        """
        # Kontrola, zda text obsahuje číslice (před preprocessingem)
        if self.contains_digit(phrase):
            return None, -1.0

        # Zkontrolujeme, zda byly vypočítány centroidy
        if self.category_centroids is None or not self.category_centroids:
            print("Chyba: Centroidy kategorií nejsou vypočítány. Spusťte metodu calculate_category_centroids().")
            return None, -1.0

        # Zakódujeme vstupní frázi (preprocessing se aplikuje automaticky v encode metodě)
        phrase_embedding = self.encode([phrase])[0]

        best_category = None
        max_similarity = -1.0 # Kosinusová podobnost je v rozsahu [-1, 1]

        # Porovnáme embedding vstupní fráze s každým centroidem kategorie
        for category_name, centroid in self.category_centroids.items():
            # Reshape centroid pro správné dimenze pro cosine_similarity
            centroid_reshaped = centroid.reshape(1, -1)
            # Reshape phrase_embedding pro správné dimenze
            phrase_embedding_reshaped = phrase_embedding.reshape(1, -1)

            # Vypočítáme kosinusovou podobnost
            similarity = cosine_similarity(phrase_embedding_reshaped, centroid_reshaped)[0][0]

            # Pokud je tato podobnost lepší než dosavadní maximum, aktualizujeme
            if similarity > max_similarity:
                max_similarity = similarity
                best_category = category_name

        # Vrátíme nejlepší kategorii a její podobnost
        if use_numeric_ids:
            # Převedeme název kategorie na číselný identifikátor
            category_id = self.get_category_id(best_category)
            return category_id, max_similarity
        else:
            # Vrátíme název kategorie
            return best_category, max_similarity

    def batch_classify(self, df, text_column='text', class_column='class', similarity_column=None, threshold=None, batch_size=None, use_numeric_ids=False):
        """
        Klasifikuje texty v DataFrame do předdefinovaných sémantických kategorií.
        Výsledky zapisuje přímo do vstupního DataFrame.
        Automaticky aplikuje preprocessing na všechny texty.

        VEKTORIZOVANÁ VERZE - výrazně rychlejší než původní implementace.

        Args:
            df (pandas.DataFrame): DataFrame obsahující texty ke klasifikaci
            text_column (str): Název sloupce obsahujícího texty ke klasifikaci (výchozí: 'text')
            class_column (str): Název sloupce, do kterého se zapíše výsledná kategorie (výchozí: 'class')
            similarity_column (str, optional): Název sloupce, do kterého se zapíše podobnost.
                                             Pokud None, podobnost se nezapisuje.
            threshold (float, optional): Práh podobnosti. Pokud je podobnost nižší než tento práh,
                                       kategorie se nastaví na None/0. Pokud None, práh se nepoužije.
            batch_size (int, optional): Velikost dávky pro kódování textů. Pokud None, použije se automatická hodnota.
            use_numeric_ids (bool): Pokud True, ukládá číselné identifikátory kategorií místo názvů.

        Returns:
            pandas.DataFrame: Vstupní DataFrame s přidanými sloupci pro výsledky klasifikace
        """
        # Zkontrolujeme, zda byly vypočtány centroidy
        if self.category_centroids is None or not self.category_centroids:
            print("Chyba: Centroidy kategorií nejsou vypočtány. Spusťte metodu calculate_category_centroids().")
            # Přidáme prázdné sloupce
            df[class_column] = None
            if similarity_column:
                df[similarity_column] = -1.0
            return df

        # Zkontrolujeme, zda existuje sloupec s texty
        if text_column not in df.columns:
            print(f"Chyba: Sloupec '{text_column}' nebyl nalezen v DataFrame.")
            return df

        # Vytvoříme kopii DataFrame, abychom nemodifikovali vstupní data
        result_df = df.copy()

        # Inicializujeme sloupce pro výsledky
        if use_numeric_ids:
            result_df[class_column] = 0  # Výchozí hodnota 0 pro číselné identifikátory
        else:
            result_df[class_column] = None  # Výchozí hodnota None pro názvy kategorií
        if similarity_column:
            result_df[similarity_column] = -1.0

        print(f"Spouštím vektorizovanou klasifikaci pro {len(result_df)} textů...")

        # VEKTORIZOVANÁ VERZE - filtrujeme platné texty
        # Vytvoříme masku pro platné texty (ne prázdné, ne None, neobsahují číslice)
        valid_mask = (
            result_df[text_column].notna() &
            (result_df[text_column] != '') &
            (~result_df[text_column].astype(str).apply(self.contains_digit))
        )

        valid_indices = result_df[valid_mask].index.tolist()
        valid_texts = result_df.loc[valid_mask, text_column].tolist()

        if not valid_texts:
            print("Nenalezeny žádné platné texty ke klasifikaci.")
            return result_df

        print(f"Nalezeno {len(valid_texts)} platných textů ke klasifikaci (z {len(result_df)} celkem).")

        # Nastavíme batch_size pokud není zadán
        if batch_size is None:
            # Použijeme optimální batch_size podle zařízení
            if self.device == 'mps':
                batch_size = 16  # Menší batch pro MPS kvůli paměti
            elif self.device == 'cuda':
                batch_size = 64  # Větší batch pro CUDA
            else:
                batch_size = 32  # Střední hodnota pro CPU

        # Zakódujeme všechny platné texty najednou
        print(f"Kóduji {len(valid_texts)} textů (batch_size={batch_size})...")
        try:
            # Použijeme tqdm pro progress bar při kódování
            with tqdm(total=len(valid_texts), desc="Kódování textů", unit="text") as pbar:
                # Pro velmi velké datasety můžeme kódovat po částech a zobrazovat progress
                if len(valid_texts) > 1000:
                    # Pro velké datasety kódujeme po částech
                    embeddings_list = []
                    chunk_size = batch_size * 10  # Zpracujeme po 10 batch-ích najednou

                    for i in range(0, len(valid_texts), chunk_size):
                        chunk_texts = valid_texts[i:i + chunk_size]
                        chunk_embeddings = self.encode(chunk_texts, batch_size=batch_size)
                        embeddings_list.append(chunk_embeddings)
                        pbar.update(len(chunk_texts))

                    embeddings = np.vstack(embeddings_list)
                else:
                    # Pro menší datasety kódujeme najednou
                    embeddings = self.encode(valid_texts, batch_size=batch_size)
                    pbar.update(len(valid_texts))

            print("Kódování dokončeno.")
        except Exception as e:
            print(f"Chyba při kódování textů: {e}")
            print("Používám fallback na jednotlivou klasifikaci...")
            return self._fallback_classify(result_df, text_column, class_column, similarity_column, threshold, use_numeric_ids)

        # Připravíme centroidy pro vektorizovaný výpočet
        category_names = list(self.category_centroids.keys())
        centroid_matrix = np.array([self.category_centroids[name] for name in category_names])

        print(f"Počítám podobnosti pro {len(embeddings)} textů s {len(category_names)} kategoriemi...")

        # Vypočítáme podobnosti pro všechny texty najednou - toto je hlavní výkonnostní výhra
        try:
            with tqdm(total=1, desc="Výpočet podobností", unit="operace") as pbar:
                similarities = cosine_similarity(embeddings, centroid_matrix)
                pbar.update(1)
            print("Výpočet podobností dokončen.")
        except Exception as e:
            print(f"Chyba při výpočtu podobností: {e}")
            print("Používám fallback na jednotlivou klasifikaci...")
            return self._fallback_classify(result_df, text_column, class_column, similarity_column, threshold, use_numeric_ids)

        # Najdeme nejlepší kategorie a jejich podobnosti
        best_indices = np.argmax(similarities, axis=1)
        max_similarities = np.max(similarities, axis=1)

        # Zapíšeme výsledky do DataFrame
        print("Zapisuji výsledky...")
        with tqdm(total=len(valid_indices), desc="Zápis výsledků", unit="řádek") as pbar:
            for i, idx in enumerate(valid_indices):
                category_name = category_names[best_indices[i]]
                similarity = max_similarities[i]

                # Aplikujeme práh, pokud je zadán
                if threshold is not None and similarity < threshold:
                    if use_numeric_ids:
                        category = 0  # Výchozí hodnota pro číselné identifikátory
                    else:
                        category = None  # Výchozí hodnota pro názvy kategorií
                else:
                    if use_numeric_ids:
                        category = self.get_category_id(category_name)
                    else:
                        category = category_name

                result_df.at[idx, class_column] = category
                if similarity_column:
                    result_df.at[idx, similarity_column] = similarity

                # Aktualizujeme progress bar každých 100 řádků pro lepší výkon
                if i % 100 == 0 or i == len(valid_indices) - 1:
                    pbar.update(min(100, len(valid_indices) - i))

        # Uvolnění paměti po náročné operaci
        self._manage_memory()

        print(f"Vektorizovaná klasifikace dokončena. Klasifikováno {len(valid_texts)} textů.")
        return result_df

    def _fallback_classify(self, df, text_column, class_column, similarity_column, threshold, use_numeric_ids=False):
        """
        Fallback metoda pro klasifikaci po jednom textu, pokud vektorizovaná verze selže.
        Používá se jako záložní řešení při chybách v batch zpracování.

        Args:
            df (pandas.DataFrame): DataFrame s inicializovanými sloupci pro výsledky
            text_column (str): Název sloupce s texty
            class_column (str): Název sloupce pro kategorie
            similarity_column (str): Název sloupce pro podobnosti (může být None)
            threshold (float): Práh podobnosti (může být None)
            use_numeric_ids (bool): Pokud True, ukládá číselné identifikátory kategorií místo názvů

        Returns:
            pandas.DataFrame: DataFrame s výsledky klasifikace
        """
        print("Používám fallback klasifikaci (text po textu)...")

        # Klasifikujeme každý řádek jednotlivě
        for idx, row in df.iterrows():
            # Získáme text ke klasifikaci
            text = row[text_column]

            # Pokud je text prázdný, None nebo obsahuje číslice, přeskočíme ho
            if pd.isna(text) or text == '' or self.contains_digit(text):
                continue

            try:
                # Klasifikujeme text s použitím správného parametru
                category, similarity = self.classify(text, use_numeric_ids=use_numeric_ids)

                # Aplikujeme práh, pokud je zadán
                if threshold is not None and similarity < threshold:
                    if use_numeric_ids:
                        category = 0  # Výchozí hodnota pro číselné identifikátory
                    else:
                        category = None  # Výchozí hodnota pro názvy kategorií

                # Zapíšeme výsledky
                df.at[idx, class_column] = category
                if similarity_column:
                    df.at[idx, similarity_column] = similarity

            except Exception as e:
                print(f"Chyba při klasifikaci textu na řádku {idx}: {e}")
                # Ponecháme výchozí hodnoty (None/0, -1.0)
                continue

        print("Fallback klasifikace dokončena.")
        return df


    def save_model(self, output_dir=None, save_centroids=True):
        """
        Uloží natrénovaný model do zadaného adresáře.
        Volitelně uloží i centroidy kategorií.

        Args:
            output_dir (str): Cesta k adresáři pro uložení modelu.
            save_centroids (bool): Zda uložit i centroidy kategorií. Výchozí je True.
        """
        # Pokud není zadán output_dir, použijeme výchozí název složky odvozený od názvu modelu
        if output_dir is None:
            output_dir = self.model_dir

        print(f"Ukládám model do: {output_dir}")
        os.makedirs(output_dir, exist_ok=True)
        self.model.save(output_dir)
        print("Model uložen. Pro použití v C++ je nutné exportovat do ONNX.")

        # Uložení centroidů, pokud jsou k dispozici a je požadováno jejich uložení
        if save_centroids and self.category_centroids:
            centroids_path = os.path.join(output_dir, 'centroids.pkl')
            self.save_centroids(centroids_path)
            print(f"Centroidy kategorií uloženy do: {centroids_path}")
        elif save_centroids:
            print("Centroidy kategorií nejsou k dispozici, nelze je uložit.")
        else:
            print("Centroidy kategorií nebyly uloženy (save_centroids=False).")

        print(f"Návod na export do ONNX z PyTorch najdete např. zde: https://pytorch.org/docs/stable/onnx.html")


    def load_model(self, model_dir=None, load_centroids=True):
        """
        Načte model - nejprve hledá lokálně, pokud nenajde, stáhne ho.

        Args:
            model_dir (str): Cesta k lokálnímu adresáři s modelem.
            load_centroids (bool): Zda načíst i centroidy kategorií. Výchozí je True.
        """
        # Pokud není zadán model_dir, použijeme výchozí název složky
        if model_dir is None:
            model_dir = self.model_dir

        print(f"Načítám model z: {model_dir}")

        # Pokus o načtení lokálního modelu
        if os.path.isdir(model_dir) and os.path.exists(os.path.join(model_dir, 'config.json')):
            try:
                print(f"Načítám model ze složky: {model_dir}")
                self.model = SentenceTransformer(model_dir, device=self.device)

                # Zobrazíme základní informace
                dimension = self.model.get_sentence_embedding_dimension()
                print(f"Model úspěšně načten (dimenze: {dimension}, zařízení: {self.device}, model: {model_dir})")

                # Resetujeme centroidy
                self.category_centroids = None

                # Načteme centroidy, pokud existují
                if load_centroids:
                    centroids_path = os.path.join(model_dir, 'centroids.pkl')
                    if os.path.exists(centroids_path):
                        self.load_centroids(centroids_path)

                return True

            except Exception as e:
                print(f"Chyba při načítání lokálního modelu: {e}")
                print("Pokusím se stáhnout model z externích zdrojů...")

        # Pokud lokální model neexistuje nebo se nepodařilo načíst, stáhneme ho
        try:
            print(f"Stahuji model: {self.model_name}")
            self.model = SentenceTransformer(self.model_name, device=self.device)

            # Zobrazíme základní informace
            dimension = self.model.get_sentence_embedding_dimension()
            print(f"Model stažen a načten (dimenze: {dimension}, zařízení: {self.device})")

            # Resetujeme centroidy
            self.category_centroids = None

            return True

        except Exception as e:
            print(f"Chyba při stahování modelu '{self.model_name}': {e}")
            self.model = None
            self.category_centroids = None
            return False


