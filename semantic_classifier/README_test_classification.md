# Test kvality klasifikace

Tento dokument popisuje použití skriptu `test_classification_quality.py` pro kontrolu kvality trénovacích dat.

## <PERSON><PERSON>el skriptu

Skript načte tréninková data z Excel souboru a otestuje, jak dobře se pozitivní a negativní příklady klasifikují proti kotvě každé kategorie. Pomáhá identifikovat:

- Špatně zvolené pozitivní příklady (nízká podobnost s kotvou)
- Špatně zvolené negativní příklady (vysoká podobnost s kotvou)
- Kategorie s malou separací mezi pozitivními a negativními příklady
- Celkovou kvalitu trénovacích dat

## Použití

### Detailní test
```bash
python test_classification_quality.py [cesta_k_excel_souboru]
```

**Příklady:**
```bash
# Použití výchozí cesty (training_data/training_set.xlsx)
python test_classification_quality.py

# Vlastní cesta k souboru
python test_classification_quality.py data/my_training_data.xlsx
```

### Rychlý test
```bash
python test_classification_quality.py --quick [cesta_k_excel_souboru]
```

**Příklady:**
```bash
# Rychlý test s výchozí cestou
python test_classification_quality.py --quick

# Rychlý test s vlastní cestou
python test_classification_quality.py --quick data/my_training_data.xlsx
```

## Výstup skriptu

### Detailní test

Pro každou kategorii zobrazí:
- **Název kategorie** a **kotvu**
- **Počet pozitivních a negativních příkladů**
- **Detailní výsledky** pro každý příklad se spolehlivostí
- **Statistiky kategorie** (průměrné spolehlivosti, úspěšnost)
- **Varování** při problémech s daty

### Rychlý test

Zobrazí pouze souhrnné statistiky:
- Počet testovaných kategorií
- Celkovou úspěšnost pozitivních příkladů
- Celkovou úspěšnost negativních příkladů
- Celkovou úspěšnost

## Interpretace výsledků

### Symboly
- ✅ **Správně klasifikováno**
  - Pozitivní: spolehlivost > 0.7
  - Negativní: spolehlivost < 0.5
- ⚠️ **Hraničně klasifikováno**
  - Pozitivní: spolehlivost 0.5-0.7
  - Negativní: spolehlivost 0.5-0.7
- ❌ **Špatně klasifikováno**
  - Pozitivní: spolehlivost < 0.5
  - Negativní: spolehlivost > 0.7

### Spolehlivost
- **Pozitivní příklady**: Spolehlivost = max(0, podobnost_s_kotvou) - vyšší je lepší
- **Negativní příklady**: Spolehlivost = max(0, podobnost_s_kotvou) - nižší je lepší

### Varování
- **Malá separace**: Rozdíl mezi průměrnou spolehlivostí pozitivních a negativních < 0.2
- **Nízká spolehlivost pozitivních**: Průměrná spolehlivost < 0.6
- **Vysoká spolehlivost negativních**: Průměrná spolehlivost > 0.6

### Doporučené hodnoty
- **Pozitivní příklady**: spolehlivost > 0.7
- **Negativní příklady**: spolehlivost < 0.5
- **Separace**: rozdíl > 0.2
- **Celková úspěšnost**: > 80%

## Řešení problémů

### Nízká spolehlivost pozitivních příkladů
- Zkontrolujte, zda pozitivní příklady skutečně patří do kategorie
- Upravte kotvu tak, aby lépe reprezentovala kategorii
- Odstraňte pozitivní příklady s velmi nízkou spolehlivostí

### Nízká spolehlivost negativních příkladů
- Zkontrolujte, zda negativní příklady skutečně nepatří do kategorie
- Přidejte více rozmanitých negativních příkladů
- Odstraňte negativní příklady s nízkou spolehlivostí

### Nízká kvalita separace
- Upravte kotvu pro lepší rozlišení
- Přidejte více charakteristických pozitivních příkladů
- Přidejte více kontrastních negativních příkladů

## Požadavky

- Python 3.6+
- sentence-transformers
- scikit-learn
- numpy
- pandas

## Struktura Excel souboru

Skript očekává Excel soubor s následující strukturou:
- **Jeden list pro každou kategorii**
- **A1**: Kotva (text pro triplet loss)
- **B1**: Hlavička "Pozitivní varianty"
- **B2 a dál**: Pozitivní příklady
- **C1**: Hlavička "Negativní příklady"  
- **C2 a dál**: Negativní příklady
