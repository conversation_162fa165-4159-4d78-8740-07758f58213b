#!/usr/bin/env python3
"""
Test script pro ověření funkčnosti standalone fine-tuning modulu.
"""

import sys
import os

# Přidáme cestu pro importy
sys.path.append('.')

def test_imports():
    """Test základních importů."""
    try:
        from semantic_classifier.fine_tune_standalone import (
            detect_device, 
            manage_memory, 
            preprocess_text,
            fine_tune_model
        )
        print("✅ Všechny importy úspěšné")
        return True
    except ImportError as e:
        print(f"❌ Chyba při importu: {e}")
        return False

def test_device_detection():
    """Test detekce zařízení."""
    try:
        from semantic_classifier.fine_tune_standalone import detect_device
        device = detect_device()
        print(f"✅ Detekované zařízení: {device}")
        return True
    except Exception as e:
        print(f"❌ Chyba při detekci zařízení: {e}")
        return False

def test_preprocessing():
    """Test preprocessing funkce."""
    try:
        from semantic_classifier.fine_tune_standalone import preprocess_text
        
        test_text = "Toto je test!!! s r<PERSON><PERSON><PERSON><PERSON><PERSON> (znaky) & symboly..."
        processed = preprocess_text(test_text)
        print(f"✅ Preprocessing test:")
        print(f"   Vstup: '{test_text}'")
        print(f"   Výstup: '{processed}'")
        return True
    except Exception as e:
        print(f"❌ Chyba při preprocessing: {e}")
        return False

def test_memory_management():
    """Test správy paměti."""
    try:
        from semantic_classifier.fine_tune_standalone import manage_memory, detect_device
        device = detect_device()
        manage_memory(device)
        print(f"✅ Správa paměti pro {device} funguje")
        return True
    except Exception as e:
        print(f"❌ Chyba při správě paměti: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testování standalone fine-tuning modulu...")
    print("=" * 50)
    
    tests = [
        ("Import test", test_imports),
        ("Device detection", test_device_detection), 
        ("Preprocessing", test_preprocessing),
        ("Memory management", test_memory_management)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}:")
        if test_func():
            passed += 1
        else:
            print(f"   Test {test_name} selhal!")
    
    print("\n" + "=" * 50)
    print(f"📊 Výsledky: {passed}/{total} testů prošlo")
    
    if passed == total:
        print("🎉 Všechny testy prošly! Standalone modul je připraven k použití.")
    else:
        print("⚠️  Některé testy selhaly. Zkontrolujte závislosti a konfiguraci.")
