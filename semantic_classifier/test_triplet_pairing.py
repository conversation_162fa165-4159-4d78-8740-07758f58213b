#!/usr/bin/env python3
"""
Test script pro ověření nové logiky párování tripletů v extended fine-tuning.
"""

import numpy as np

def test_old_vs_new_pairing():
    """Test porovnání staré a nové logiky p<PERSON>."""
    print("🧪 Testování staré vs. nové logiky párování...")
    
    # Simulujeme scénáře
    scenarios = [
        {
            "name": "Stejný počet pozitiv a negativ",
            "positives": 5,
            "negatives": 5,
            "expected_old": 25,  # 5×5
            "expected_new": 5    # min(5,5)
        },
        {
            "name": "Více pozitiv než negativ",
            "positives": 5,
            "negatives": 3,
            "expected_old": 15,  # 5×3
            "expected_new": 3    # min(5,3)
        },
        {
            "name": "Více negativ než pozitiv",
            "positives": 2,
            "negatives": 5,
            "expected_old": 10,  # 2×5
            "expected_new": 2    # min(2,5)
        },
        {
            "name": "<PERSON><PERSON> pozitivum, více negativ",
            "positives": 1,
            "negatives": 5,
            "expected_old": 5,   # 1×5
            "expected_new": 1    # min(1,5)
        }
    ]
    
    print("   Porovnání počtu tripletů:")
    print("   " + "-" * 60)
    print(f"   {'Scénář':<30} {'Stará':<8} {'Nová':<8} {'Úspora':<10}")
    print("   " + "-" * 60)
    
    total_old = 0
    total_new = 0
    
    for scenario in scenarios:
        old_count = scenario["expected_old"]
        new_count = scenario["expected_new"]
        savings = old_count - new_count
        savings_percent = (savings / old_count * 100) if old_count > 0 else 0
        
        total_old += old_count
        total_new += new_count
        
        print(f"   {scenario['name']:<30} {old_count:<8} {new_count:<8} {savings} ({savings_percent:.0f}%)")
    
    total_savings = total_old - total_new
    total_savings_percent = (total_savings / total_old * 100) if total_old > 0 else 0
    
    print("   " + "-" * 60)
    print(f"   {'CELKEM':<30} {total_old:<8} {total_new:<8} {total_savings} ({total_savings_percent:.0f}%)")
    
    print(f"\n✅ Nová logika výrazně snižuje počet tripletů!")
    return True

def test_pairing_logic():
    """Test konkrétní logiky párování."""
    print("\n🧪 Testování konkrétní logiky párování...")
    
    # Simulujeme seřazené indexy (nejhorší první)
    selected_positive_indices = np.array([2, 0, 4])  # 3 nejhorší pozitiva
    selected_negative_indices = np.array([1, 3, 0, 2, 4])  # 5 nejhorších negativ
    
    # Simulujeme texty
    positives = ["pos_0", "pos_1", "pos_2", "pos_3", "pos_4"]
    negatives = ["neg_0", "neg_1", "neg_2", "neg_3", "neg_4"]
    anchor_text = "anchor"
    
    print(f"   Pozitiva (indexy): {selected_positive_indices}")
    print(f"   Negativa (indexy): {selected_negative_indices}")
    
    # Nová logika párování
    num_triplets = min(len(selected_positive_indices), len(selected_negative_indices))
    print(f"   Počet tripletů: min({len(selected_positive_indices)}, {len(selected_negative_indices)}) = {num_triplets}")
    
    triplets = []
    for i in range(num_triplets):
        pos_idx = selected_positive_indices[i]
        neg_idx = selected_negative_indices[i]
        
        positive_text = positives[pos_idx]
        negative_text = negatives[neg_idx]
        
        triplet = [anchor_text, positive_text, negative_text]
        triplets.append(triplet)
    
    print(f"   Vytvořené triplety:")
    for i, triplet in enumerate(triplets, 1):
        print(f"     {i}. {triplet}")
    
    # Ověříme, že párování je správné
    expected_pairs = [
        (selected_positive_indices[0], selected_negative_indices[0]),
        (selected_positive_indices[1], selected_negative_indices[1]),
        (selected_positive_indices[2], selected_negative_indices[2])
    ]
    
    print(f"   Očekávané páry (pos_idx, neg_idx): {expected_pairs}")
    
    if len(triplets) == num_triplets:
        print("✅ Párování funguje správně!")
        return True
    else:
        print("❌ Chyba v párování!")
        return False

def test_edge_cases():
    """Test krajních případů."""
    print("\n🧪 Testování krajních případů...")
    
    edge_cases = [
        {
            "name": "Žádná pozitiva",
            "positives": 0,
            "negatives": 5,
            "expected": 0
        },
        {
            "name": "Žádná negativa", 
            "positives": 5,
            "negatives": 0,
            "expected": 0
        },
        {
            "name": "Žádná pozitiva ani negativa",
            "positives": 0,
            "negatives": 0,
            "expected": 0
        },
        {
            "name": "Jedno pozitivum, jedno negativum",
            "positives": 1,
            "negatives": 1,
            "expected": 1
        }
    ]
    
    for case in edge_cases:
        print(f"   Případ: {case['name']}")
        
        # Simulujeme logiku
        if case['positives'] > 0 and case['negatives'] > 0:
            num_triplets = min(case['positives'], case['negatives'])
        else:
            num_triplets = 0
        
        print(f"     Pozitiva: {case['positives']}, Negativa: {case['negatives']}")
        print(f"     Očekáváno: {case['expected']}, Vypočteno: {num_triplets}")
        
        if num_triplets == case['expected']:
            print(f"     ✅ OK")
        else:
            print(f"     ❌ Chyba!")
            return False
    
    print("✅ Všechny krajní případy fungují správně!")
    return True

def test_memory_impact():
    """Test dopadu na paměť."""
    print("\n🧪 Testování dopadu na paměť...")
    
    # Simulujeme reálný scénář s více třídami
    classes_scenario = {
        "num_classes": 10,
        "avg_positives": 5,
        "avg_negatives": 5
    }
    
    # Stará logika (kartézský součin)
    old_triplets_per_class = classes_scenario["avg_positives"] * classes_scenario["avg_negatives"]
    old_total_triplets = old_triplets_per_class * classes_scenario["num_classes"]
    
    # Nová logika (párování 1:1)
    new_triplets_per_class = min(classes_scenario["avg_positives"], classes_scenario["avg_negatives"])
    new_total_triplets = new_triplets_per_class * classes_scenario["num_classes"]
    
    # Odhad paměťové náročnosti (velmi zjednodušený)
    # Předpokládáme, že každý triplet zabere ~1KB v paměti
    old_memory_mb = old_total_triplets * 1 / 1024  # KB -> MB
    new_memory_mb = new_total_triplets * 1 / 1024  # KB -> MB
    
    memory_savings = old_memory_mb - new_memory_mb
    memory_savings_percent = (memory_savings / old_memory_mb * 100) if old_memory_mb > 0 else 0
    
    print(f"   Scénář: {classes_scenario['num_classes']} tříd, průměrně {classes_scenario['avg_positives']} pozitiv a {classes_scenario['avg_negatives']} negativ")
    print(f"   Stará logika:")
    print(f"     Tripletů na třídu: {old_triplets_per_class}")
    print(f"     Celkem tripletů: {old_total_triplets}")
    print(f"     Odhad paměti: {old_memory_mb:.1f} MB")
    print(f"   Nová logika:")
    print(f"     Tripletů na třídu: {new_triplets_per_class}")
    print(f"     Celkem tripletů: {new_total_triplets}")
    print(f"     Odhad paměti: {new_memory_mb:.1f} MB")
    print(f"   Úspora:")
    print(f"     Tripletů: {old_total_triplets - new_total_triplets} ({(old_total_triplets - new_total_triplets) / old_total_triplets * 100:.0f}%)")
    print(f"     Paměti: {memory_savings:.1f} MB ({memory_savings_percent:.0f}%)")
    
    if memory_savings > 0:
        print("✅ Nová logika výrazně šetří paměť!")
        return True
    else:
        print("❌ Problém s výpočtem úspor!")
        return False

if __name__ == "__main__":
    print("🔄 Test nové logiky párování tripletů")
    print("=" * 60)
    
    tests = [
        ("Old vs New pairing comparison", test_old_vs_new_pairing),
        ("Pairing logic", test_pairing_logic),
        ("Edge cases", test_edge_cases),
        ("Memory impact", test_memory_impact)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"🔍 {test_name}:")
        print("-" * 40)
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PROŠEL")
            else:
                print(f"❌ {test_name} - SELHAL")
        except Exception as e:
            print(f"❌ {test_name} - CHYBA: {e}")
        print()
    
    print("=" * 60)
    print(f"📊 VÝSLEDKY: {passed}/{total} testů prošlo")
    
    if passed == total:
        print("🎉 Všechny testy nové logiky párování prošly!")
        print("\n💡 Výhody nové logiky:")
        print("   🔢 Výrazně méně tripletů (80% úspora)")
        print("   💾 Nižší paměťová náročnost")
        print("   ⚡ Rychlejší trénink")
        print("   🎯 Efektivnější využití nejhorších příkladů")
        print("   🧹 Lepší správa paměti")
    else:
        print("⚠️  Některé testy selhaly.")
